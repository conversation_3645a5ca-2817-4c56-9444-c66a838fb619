"""
Shared validation utilities.

This module provides common validation functions that are used
across multiple components.
"""

import ast
import json
import re
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
import jsonschema


def validate_python_syntax(code: str) -> Dict[str, Any]:
    """
    Validate Python code syntax.
    
    Args:
        code: Python code string
        
    Returns:
        Dictionary with validation results
    """
    result = {
        "is_valid": False,
        "error": None,
        "line_number": None,
        "column": None
    }
    
    try:
        ast.parse(code)
        result["is_valid"] = True
    except SyntaxError as e:
        result["error"] = str(e)
        result["line_number"] = e.lineno
        result["column"] = e.offset
    except Exception as e:
        result["error"] = f"Unexpected error: {str(e)}"
    
    return result


def validate_json_schema(data: Any, schema: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate data against a JSON schema.
    
    Args:
        data: Data to validate
        schema: JSON schema
        
    Returns:
        Dictionary with validation results
    """
    result = {
        "is_valid": False,
        "errors": [],
        "warnings": []
    }
    
    try:
        jsonschema.validate(data, schema)
        result["is_valid"] = True
    except jsonschema.ValidationError as e:
        result["errors"].append({
            "message": e.message,
            "path": list(e.absolute_path),
            "schema_path": list(e.schema_path)
        })
    except jsonschema.SchemaError as e:
        result["errors"].append({
            "message": f"Invalid schema: {e.message}",
            "path": [],
            "schema_path": list(e.schema_path)
        })
    except Exception as e:
        result["errors"].append({
            "message": f"Validation error: {str(e)}",
            "path": [],
            "schema_path": []
        })
    
    return result


def validate_file_path(file_path: Union[str, Path], 
                      must_exist: bool = False,
                      must_be_file: bool = False,
                      must_be_dir: bool = False,
                      readable: bool = False,
                      writable: bool = False) -> Dict[str, Any]:
    """
    Validate file path with various constraints.
    
    Args:
        file_path: Path to validate
        must_exist: Whether path must exist
        must_be_file: Whether path must be a file
        must_be_dir: Whether path must be a directory
        readable: Whether path must be readable
        writable: Whether path must be writable
        
    Returns:
        Dictionary with validation results
    """
    result = {
        "is_valid": True,
        "errors": [],
        "warnings": [],
        "path_info": {}
    }
    
    try:
        path = Path(file_path)
        
        # Basic path info
        result["path_info"] = {
            "absolute_path": str(path.absolute()),
            "exists": path.exists(),
            "is_file": path.is_file() if path.exists() else None,
            "is_dir": path.is_dir() if path.exists() else None,
            "is_symlink": path.is_symlink() if path.exists() else None
        }
        
        # Check existence
        if must_exist and not path.exists():
            result["errors"].append(f"Path does not exist: {path}")
            result["is_valid"] = False
        
        # Check if it's a file
        if must_be_file and path.exists() and not path.is_file():
            result["errors"].append(f"Path is not a file: {path}")
            result["is_valid"] = False
        
        # Check if it's a directory
        if must_be_dir and path.exists() and not path.is_dir():
            result["errors"].append(f"Path is not a directory: {path}")
            result["is_valid"] = False
        
        # Check readability
        if readable and path.exists():
            try:
                if path.is_file():
                    with open(path, 'r') as f:
                        f.read(1)
                elif path.is_dir():
                    list(path.iterdir())
            except (IOError, OSError, PermissionError):
                result["errors"].append(f"Path is not readable: {path}")
                result["is_valid"] = False
        
        # Check writability
        if writable:
            try:
                if path.exists():
                    if path.is_file():
                        # Try to open in append mode
                        with open(path, 'a') as f:
                            pass
                    elif path.is_dir():
                        # Try to create a temp file in the directory
                        temp_file = path / ".write_test"
                        temp_file.touch()
                        temp_file.unlink()
                else:
                    # Try to create the file/directory
                    if must_be_dir:
                        path.mkdir(parents=True, exist_ok=True)
                        path.rmdir()
                    else:
                        path.parent.mkdir(parents=True, exist_ok=True)
                        path.touch()
                        path.unlink()
            except (IOError, OSError, PermissionError):
                result["errors"].append(f"Path is not writable: {path}")
                result["is_valid"] = False
        
    except Exception as e:
        result["errors"].append(f"Path validation error: {str(e)}")
        result["is_valid"] = False
    
    return result


def sanitize_input(input_str: str, 
                  max_length: Optional[int] = None,
                  allowed_chars: Optional[str] = None,
                  remove_html: bool = True,
                  remove_sql: bool = True) -> str:
    """
    Sanitize user input for security.
    
    Args:
        input_str: Input string to sanitize
        max_length: Maximum allowed length
        allowed_chars: Regex pattern for allowed characters
        remove_html: Whether to remove HTML tags
        remove_sql: Whether to remove SQL injection patterns
        
    Returns:
        Sanitized string
    """
    if not isinstance(input_str, str):
        return ""
    
    sanitized = input_str
    
    # Remove HTML tags
    if remove_html:
        sanitized = re.sub(r'<[^>]+>', '', sanitized)
    
    # Remove SQL injection patterns
    if remove_sql:
        sql_patterns = [
            r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)',
            r'(--|#|/\*|\*/)',
            r'(\bOR\b.*=.*\bOR\b)',
            r'(\bAND\b.*=.*\bAND\b)',
            r'(\'.*\')',
            r'(;.*)',
        ]
        for pattern in sql_patterns:
            sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)
    
    # Apply character whitelist
    if allowed_chars:
        sanitized = re.sub(f'[^{allowed_chars}]', '', sanitized)
    
    # Truncate to max length
    if max_length and len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
    
    return sanitized.strip()


def validate_email(email: str) -> bool:
    """
    Validate email address format.
    
    Args:
        email: Email address to validate
        
    Returns:
        True if valid email format
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_url(url: str) -> bool:
    """
    Validate URL format.
    
    Args:
        url: URL to validate
        
    Returns:
        True if valid URL format
    """
    pattern = r'^https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$'
    return bool(re.match(pattern, url))


def validate_identifier(identifier: str, allow_keywords: bool = False) -> Dict[str, Any]:
    """
    Validate Python identifier.
    
    Args:
        identifier: Identifier to validate
        allow_keywords: Whether to allow Python keywords
        
    Returns:
        Dictionary with validation results
    """
    import keyword
    
    result = {
        "is_valid": True,
        "errors": [],
        "warnings": []
    }
    
    # Check if it's a valid identifier
    if not identifier.isidentifier():
        result["errors"].append("Not a valid Python identifier")
        result["is_valid"] = False
        return result
    
    # Check if it's a keyword
    if keyword.iskeyword(identifier):
        if allow_keywords:
            result["warnings"].append("Identifier is a Python keyword")
        else:
            result["errors"].append("Identifier cannot be a Python keyword")
            result["is_valid"] = False
    
    # Check naming conventions
    if identifier.startswith('_'):
        result["warnings"].append("Identifier starts with underscore (private convention)")
    
    if identifier.isupper():
        result["warnings"].append("Identifier is all uppercase (constant convention)")
    
    return result


def validate_version(version: str) -> Dict[str, Any]:
    """
    Validate semantic version string.
    
    Args:
        version: Version string to validate
        
    Returns:
        Dictionary with validation results
    """
    result = {
        "is_valid": False,
        "errors": [],
        "parsed": None
    }
    
    # Semantic version pattern: MAJOR.MINOR.PATCH[-PRERELEASE][+BUILD]
    pattern = r'^(\d+)\.(\d+)\.(\d+)(?:-([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?(?:\+([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?$'
    
    match = re.match(pattern, version)
    if match:
        result["is_valid"] = True
        result["parsed"] = {
            "major": int(match.group(1)),
            "minor": int(match.group(2)),
            "patch": int(match.group(3)),
            "prerelease": match.group(4),
            "build": match.group(5)
        }
    else:
        result["errors"].append("Invalid semantic version format")
    
    return result


def validate_json_string(json_str: str) -> Dict[str, Any]:
    """
    Validate JSON string.
    
    Args:
        json_str: JSON string to validate
        
    Returns:
        Dictionary with validation results
    """
    result = {
        "is_valid": False,
        "error": None,
        "parsed": None
    }
    
    try:
        parsed = json.loads(json_str)
        result["is_valid"] = True
        result["parsed"] = parsed
    except json.JSONDecodeError as e:
        result["error"] = str(e)
    except Exception as e:
        result["error"] = f"Unexpected error: {str(e)}"
    
    return result


def validate_regex_pattern(pattern: str) -> Dict[str, Any]:
    """
    Validate regular expression pattern.
    
    Args:
        pattern: Regex pattern to validate
        
    Returns:
        Dictionary with validation results
    """
    result = {
        "is_valid": False,
        "error": None,
        "compiled": None
    }
    
    try:
        compiled_pattern = re.compile(pattern)
        result["is_valid"] = True
        result["compiled"] = compiled_pattern
    except re.error as e:
        result["error"] = str(e)
    except Exception as e:
        result["error"] = f"Unexpected error: {str(e)}"
    
    return result


def validate_port_number(port: Union[int, str]) -> bool:
    """
    Validate port number.
    
    Args:
        port: Port number to validate
        
    Returns:
        True if valid port number
    """
    try:
        port_int = int(port)
        return 1 <= port_int <= 65535
    except (ValueError, TypeError):
        return False


def validate_ip_address(ip: str) -> Dict[str, Any]:
    """
    Validate IP address (IPv4 or IPv6).
    
    Args:
        ip: IP address to validate
        
    Returns:
        Dictionary with validation results
    """
    import ipaddress
    
    result = {
        "is_valid": False,
        "version": None,
        "error": None
    }
    
    try:
        addr = ipaddress.ip_address(ip)
        result["is_valid"] = True
        result["version"] = addr.version
    except ValueError as e:
        result["error"] = str(e)
    except Exception as e:
        result["error"] = f"Unexpected error: {str(e)}"
    
    return result
