"""
Shared async utilities.

This module provides common async patterns and utilities
that are used across multiple components.
"""

import asyncio
import functools
import time
from typing import Any, Awaitable, Callable, List, Optional, TypeVar, Union
from contextlib import asynccontextmanager

T = TypeVar('T')


async def run_with_timeout(coro: Awaitable[T], timeout: float) -> T:
    """
    Run a coroutine with a timeout.
    
    Args:
        coro: Coroutine to run
        timeout: Timeout in seconds
        
    Returns:
        Result of the coroutine
        
    Raises:
        asyncio.TimeoutError: If timeout is exceeded
    """
    return await asyncio.wait_for(coro, timeout=timeout)


async def gather_with_limit(coroutines: List[Awaitable[T]], limit: int = 10) -> List[T]:
    """
    Run coroutines concurrently with a concurrency limit.
    
    Args:
        coroutines: List of coroutines to run
        limit: Maximum number of concurrent coroutines
        
    Returns:
        List of results in the same order as input
    """
    semaphore = asyncio.Semaphore(limit)
    
    async def run_with_semaphore(coro: Awaitable[T]) -> T:
        async with semaphore:
            return await coro
    
    limited_coroutines = [run_with_semaphore(coro) for coro in coroutines]
    return await asyncio.gather(*limited_coroutines)


async def retry_async(
    func: Callable[..., Awaitable[T]],
    *args,
    max_retries: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: tuple = (Exception,),
    **kwargs
) -> T:
    """
    Retry an async function with exponential backoff.
    
    Args:
        func: Async function to retry
        *args: Positional arguments for the function
        max_retries: Maximum number of retries
        delay: Initial delay between retries
        backoff_factor: Factor to multiply delay by after each retry
        exceptions: Tuple of exceptions to catch and retry on
        **kwargs: Keyword arguments for the function
        
    Returns:
        Result of the function
        
    Raises:
        The last exception if all retries fail
    """
    last_exception = None
    current_delay = delay
    
    for attempt in range(max_retries + 1):
        try:
            return await func(*args, **kwargs)
        except exceptions as e:
            last_exception = e
            
            if attempt < max_retries:
                await asyncio.sleep(current_delay)
                current_delay *= backoff_factor
            else:
                raise last_exception


@asynccontextmanager
async def create_task_group():
    """
    Create a task group for managing multiple tasks.
    
    This is a simplified version of asyncio.TaskGroup for older Python versions.
    """
    tasks = []
    
    try:
        yield tasks
        
        # Wait for all tasks to complete
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    finally:
        # Cancel any remaining tasks
        for task in tasks:
            if not task.done():
                task.cancel()
        
        # Wait for cancellation to complete
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)


async def safe_await(coro: Awaitable[T], default: T = None) -> T:
    """
    Safely await a coroutine, returning a default value on exception.
    
    Args:
        coro: Coroutine to await
        default: Default value to return on exception
        
    Returns:
        Result of coroutine or default value
    """
    try:
        return await coro
    except Exception:
        return default


def async_lru_cache(maxsize: int = 128):
    """
    LRU cache decorator for async functions.
    
    Args:
        maxsize: Maximum cache size
        
    Returns:
        Decorator function
    """
    def decorator(func: Callable[..., Awaitable[T]]) -> Callable[..., Awaitable[T]]:
        cache = {}
        access_order = []
        
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Create cache key
            key = (args, tuple(sorted(kwargs.items())))
            
            # Check if result is in cache
            if key in cache:
                # Move to end (most recently used)
                access_order.remove(key)
                access_order.append(key)
                return cache[key]
            
            # Call function and cache result
            result = await func(*args, **kwargs)
            
            # Add to cache
            cache[key] = result
            access_order.append(key)
            
            # Remove oldest items if cache is full
            while len(cache) > maxsize:
                oldest_key = access_order.pop(0)
                del cache[oldest_key]
            
            return result
        
        # Add cache management methods
        wrapper.cache_clear = lambda: (cache.clear(), access_order.clear())
        wrapper.cache_info = lambda: {
            'hits': 0,  # Would need to track this
            'misses': 0,  # Would need to track this
            'maxsize': maxsize,
            'currsize': len(cache)
        }
        
        return wrapper
    
    return decorator


async def debounce_async(func: Callable[..., Awaitable[T]], delay: float) -> Callable[..., Awaitable[T]]:
    """
    Debounce an async function.
    
    Args:
        func: Async function to debounce
        delay: Delay in seconds
        
    Returns:
        Debounced function
    """
    last_call_time = 0
    task = None
    
    async def debounced(*args, **kwargs):
        nonlocal last_call_time, task
        
        current_time = time.time()
        last_call_time = current_time
        
        # Cancel previous task if it exists
        if task and not task.done():
            task.cancel()
        
        async def delayed_call():
            await asyncio.sleep(delay)
            # Check if this is still the latest call
            if time.time() - last_call_time >= delay - 0.001:  # Small tolerance
                return await func(*args, **kwargs)
        
        task = asyncio.create_task(delayed_call())
        return await task
    
    return debounced


async def throttle_async(func: Callable[..., Awaitable[T]], rate: float) -> Callable[..., Awaitable[T]]:
    """
    Throttle an async function to limit call rate.
    
    Args:
        func: Async function to throttle
        rate: Maximum calls per second
        
    Returns:
        Throttled function
    """
    min_interval = 1.0 / rate
    last_call_time = 0
    
    async def throttled(*args, **kwargs):
        nonlocal last_call_time
        
        current_time = time.time()
        time_since_last_call = current_time - last_call_time
        
        if time_since_last_call < min_interval:
            await asyncio.sleep(min_interval - time_since_last_call)
        
        last_call_time = time.time()
        return await func(*args, **kwargs)
    
    return throttled


async def async_map(func: Callable[[T], Awaitable[Any]], items: List[T], 
                   concurrency: int = 10) -> List[Any]:
    """
    Apply an async function to a list of items with concurrency control.
    
    Args:
        func: Async function to apply
        items: List of items to process
        concurrency: Maximum concurrent operations
        
    Returns:
        List of results
    """
    semaphore = asyncio.Semaphore(concurrency)
    
    async def process_item(item: T):
        async with semaphore:
            return await func(item)
    
    tasks = [process_item(item) for item in items]
    return await asyncio.gather(*tasks)


async def async_filter(func: Callable[[T], Awaitable[bool]], items: List[T],
                      concurrency: int = 10) -> List[T]:
    """
    Filter a list using an async predicate function.
    
    Args:
        func: Async predicate function
        items: List of items to filter
        concurrency: Maximum concurrent operations
        
    Returns:
        Filtered list
    """
    results = await async_map(func, items, concurrency)
    return [item for item, keep in zip(items, results) if keep]


class AsyncContextManager:
    """
    Base class for async context managers.
    """
    
    async def __aenter__(self):
        await self.setup()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()
    
    async def setup(self):
        """Override this method to implement setup logic."""
        pass
    
    async def cleanup(self):
        """Override this method to implement cleanup logic."""
        pass


class AsyncLock:
    """
    Async lock with timeout support.
    """
    
    def __init__(self):
        self._lock = asyncio.Lock()
    
    async def acquire(self, timeout: Optional[float] = None):
        """Acquire the lock with optional timeout."""
        if timeout is None:
            await self._lock.acquire()
        else:
            await asyncio.wait_for(self._lock.acquire(), timeout=timeout)
    
    def release(self):
        """Release the lock."""
        self._lock.release()
    
    async def __aenter__(self):
        await self.acquire()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        self.release()


class AsyncQueue:
    """
    Async queue with additional features.
    """
    
    def __init__(self, maxsize: int = 0):
        self._queue = asyncio.Queue(maxsize=maxsize)
        self._closed = False
    
    async def put(self, item: T, timeout: Optional[float] = None):
        """Put an item in the queue with optional timeout."""
        if self._closed:
            raise RuntimeError("Queue is closed")
        
        if timeout is None:
            await self._queue.put(item)
        else:
            await asyncio.wait_for(self._queue.put(item), timeout=timeout)
    
    async def get(self, timeout: Optional[float] = None) -> T:
        """Get an item from the queue with optional timeout."""
        if timeout is None:
            return await self._queue.get()
        else:
            return await asyncio.wait_for(self._queue.get(), timeout=timeout)
    
    def close(self):
        """Close the queue."""
        self._closed = True
    
    @property
    def closed(self) -> bool:
        """Check if the queue is closed."""
        return self._closed
    
    def qsize(self) -> int:
        """Get the current queue size."""
        return self._queue.qsize()
    
    def empty(self) -> bool:
        """Check if the queue is empty."""
        return self._queue.empty()
    
    def full(self) -> bool:
        """Check if the queue is full."""
        return self._queue.full()


async def wait_for_condition(condition: Callable[[], bool], 
                            timeout: float = 10.0,
                            check_interval: float = 0.1) -> bool:
    """
    Wait for a condition to become true.
    
    Args:
        condition: Function that returns True when condition is met
        timeout: Maximum time to wait
        check_interval: How often to check the condition
        
    Returns:
        True if condition was met, False if timeout
    """
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        if condition():
            return True
        await asyncio.sleep(check_interval)
    
    return False
