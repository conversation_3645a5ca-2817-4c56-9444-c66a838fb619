"""
Shared file utilities.

This module provides common file operations and handling functions
that are used across multiple components.
"""

import os
import shutil
import tempfile
from pathlib import Path
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
import hashlib
import json


def read_file_safe(file_path: Union[str, Path], encoding: str = 'utf-8') -> Optional[str]:
    """
    Safely read a file with error handling.
    
    Args:
        file_path: Path to the file
        encoding: File encoding
        
    Returns:
        File contents or None if error
    """
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            return f.read()
    except (IOError, OSError, UnicodeDecodeError):
        return None


def write_file_safe(file_path: Union[str, Path], content: str, 
                   encoding: str = 'utf-8', backup: bool = False) -> bool:
    """
    Safely write content to a file with error handling.
    
    Args:
        file_path: Path to the file
        content: Content to write
        encoding: File encoding
        backup: Whether to create a backup
        
    Returns:
        True if successful, False otherwise
    """
    try:
        file_path = Path(file_path)
        
        # Create backup if requested
        if backup and file_path.exists():
            backup_file(file_path)
        
        # Ensure directory exists
        ensure_directory(file_path.parent)
        
        # Write file
        with open(file_path, 'w', encoding=encoding) as f:
            f.write(content)
        
        return True
    except (IOError, OSError, UnicodeEncodeError):
        return False


def ensure_directory(dir_path: Union[str, Path]) -> bool:
    """
    Ensure a directory exists, creating it if necessary.
    
    Args:
        dir_path: Path to the directory
        
    Returns:
        True if directory exists or was created, False otherwise
    """
    try:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        return True
    except (IOError, OSError):
        return False


def get_file_info(file_path: Union[str, Path]) -> Optional[Dict[str, Any]]:
    """
    Get comprehensive file information.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Dictionary with file information or None if error
    """
    try:
        file_path = Path(file_path)
        
        if not file_path.exists():
            return None
        
        stat = file_path.stat()
        
        info = {
            "path": str(file_path.absolute()),
            "name": file_path.name,
            "stem": file_path.stem,
            "suffix": file_path.suffix,
            "size_bytes": stat.st_size,
            "created_at": datetime.fromtimestamp(stat.st_ctime),
            "modified_at": datetime.fromtimestamp(stat.st_mtime),
            "accessed_at": datetime.fromtimestamp(stat.st_atime),
            "is_file": file_path.is_file(),
            "is_directory": file_path.is_dir(),
            "is_symlink": file_path.is_symlink(),
            "permissions": oct(stat.st_mode)[-3:]
        }
        
        # Add file hash for files
        if file_path.is_file():
            info["md5_hash"] = get_file_hash(file_path)
        
        return info
        
    except (IOError, OSError):
        return None


def get_file_hash(file_path: Union[str, Path], algorithm: str = 'md5') -> Optional[str]:
    """
    Calculate file hash.
    
    Args:
        file_path: Path to the file
        algorithm: Hash algorithm (md5, sha1, sha256)
        
    Returns:
        File hash or None if error
    """
    try:
        hash_obj = hashlib.new(algorithm)
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        
        return hash_obj.hexdigest()
        
    except (IOError, OSError, ValueError):
        return None


def find_files(directory: Union[str, Path], pattern: str = "*", 
               recursive: bool = True, include_dirs: bool = False) -> List[Path]:
    """
    Find files matching a pattern.
    
    Args:
        directory: Directory to search
        pattern: File pattern (glob style)
        recursive: Whether to search recursively
        include_dirs: Whether to include directories
        
    Returns:
        List of matching file paths
    """
    try:
        directory = Path(directory)
        
        if not directory.exists() or not directory.is_dir():
            return []
        
        if recursive:
            matches = directory.rglob(pattern)
        else:
            matches = directory.glob(pattern)
        
        results = []
        for match in matches:
            if include_dirs or match.is_file():
                results.append(match)
        
        return sorted(results)
        
    except (IOError, OSError):
        return []


def backup_file(file_path: Union[str, Path], backup_dir: Optional[Union[str, Path]] = None) -> Optional[Path]:
    """
    Create a backup of a file.
    
    Args:
        file_path: Path to the file to backup
        backup_dir: Directory for backup (default: same directory)
        
    Returns:
        Path to backup file or None if error
    """
    try:
        file_path = Path(file_path)
        
        if not file_path.exists() or not file_path.is_file():
            return None
        
        # Determine backup location
        if backup_dir:
            backup_dir = Path(backup_dir)
            ensure_directory(backup_dir)
            backup_path = backup_dir / f"{file_path.stem}.backup{file_path.suffix}"
        else:
            backup_path = file_path.with_suffix(f".backup{file_path.suffix}")
        
        # Add timestamp if backup already exists
        if backup_path.exists():
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = backup_path.with_stem(f"{backup_path.stem}_{timestamp}")
        
        # Copy file
        shutil.copy2(file_path, backup_path)
        return backup_path
        
    except (IOError, OSError):
        return None


def copy_file(source: Union[str, Path], destination: Union[str, Path], 
              overwrite: bool = False) -> bool:
    """
    Copy a file to a destination.
    
    Args:
        source: Source file path
        destination: Destination file path
        overwrite: Whether to overwrite existing files
        
    Returns:
        True if successful, False otherwise
    """
    try:
        source = Path(source)
        destination = Path(destination)
        
        if not source.exists() or not source.is_file():
            return False
        
        if destination.exists() and not overwrite:
            return False
        
        # Ensure destination directory exists
        ensure_directory(destination.parent)
        
        # Copy file
        shutil.copy2(source, destination)
        return True
        
    except (IOError, OSError):
        return False


def move_file(source: Union[str, Path], destination: Union[str, Path], 
              overwrite: bool = False) -> bool:
    """
    Move a file to a destination.
    
    Args:
        source: Source file path
        destination: Destination file path
        overwrite: Whether to overwrite existing files
        
    Returns:
        True if successful, False otherwise
    """
    try:
        source = Path(source)
        destination = Path(destination)
        
        if not source.exists():
            return False
        
        if destination.exists() and not overwrite:
            return False
        
        # Ensure destination directory exists
        ensure_directory(destination.parent)
        
        # Move file
        shutil.move(str(source), str(destination))
        return True
        
    except (IOError, OSError):
        return False


def delete_file(file_path: Union[str, Path], backup: bool = False) -> bool:
    """
    Delete a file with optional backup.
    
    Args:
        file_path: Path to the file to delete
        backup: Whether to create a backup before deletion
        
    Returns:
        True if successful, False otherwise
    """
    try:
        file_path = Path(file_path)
        
        if not file_path.exists():
            return True  # Already deleted
        
        # Create backup if requested
        if backup:
            backup_file(file_path)
        
        # Delete file
        file_path.unlink()
        return True
        
    except (IOError, OSError):
        return False


def clean_directory(directory: Union[str, Path], pattern: str = "*", 
                   recursive: bool = False, dry_run: bool = False) -> List[Path]:
    """
    Clean files from a directory matching a pattern.
    
    Args:
        directory: Directory to clean
        pattern: File pattern to match
        recursive: Whether to clean recursively
        dry_run: If True, return files that would be deleted without deleting
        
    Returns:
        List of files that were (or would be) deleted
    """
    try:
        files_to_delete = find_files(directory, pattern, recursive, include_dirs=False)
        
        if dry_run:
            return files_to_delete
        
        deleted_files = []
        for file_path in files_to_delete:
            try:
                file_path.unlink()
                deleted_files.append(file_path)
            except (IOError, OSError):
                continue
        
        return deleted_files
        
    except (IOError, OSError):
        return []


def get_directory_size(directory: Union[str, Path]) -> int:
    """
    Calculate total size of a directory.
    
    Args:
        directory: Directory path
        
    Returns:
        Total size in bytes
    """
    try:
        directory = Path(directory)
        total_size = 0
        
        for file_path in directory.rglob('*'):
            if file_path.is_file():
                total_size += file_path.stat().st_size
        
        return total_size
        
    except (IOError, OSError):
        return 0


def create_temp_file(suffix: str = "", prefix: str = "tmp", 
                    content: Optional[str] = None) -> Optional[Path]:
    """
    Create a temporary file.
    
    Args:
        suffix: File suffix
        prefix: File prefix
        content: Optional content to write
        
    Returns:
        Path to temporary file or None if error
    """
    try:
        fd, temp_path = tempfile.mkstemp(suffix=suffix, prefix=prefix)
        temp_path = Path(temp_path)
        
        # Close the file descriptor
        os.close(fd)
        
        # Write content if provided
        if content is not None:
            write_file_safe(temp_path, content)
        
        return temp_path
        
    except (IOError, OSError):
        return None


def create_temp_directory(prefix: str = "tmp") -> Optional[Path]:
    """
    Create a temporary directory.
    
    Args:
        prefix: Directory prefix
        
    Returns:
        Path to temporary directory or None if error
    """
    try:
        temp_dir = tempfile.mkdtemp(prefix=prefix)
        return Path(temp_dir)
    except (IOError, OSError):
        return None
