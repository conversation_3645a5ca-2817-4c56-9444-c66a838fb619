"""
Advanced Progress Tracking System for CLI operations.

Provides comprehensive progress tracking with real-time updates, estimated time remaining,
and visual progress indicators using rich's Progress class.
"""

import time
import threading
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Callable, Iterator
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

try:
    from rich.progress import (
        Progress, TaskID, SpinnerColumn, TextColumn, BarColumn, 
        TimeElapsedColumn, TimeRemainingColumn, MofNCompleteColumn,
        ProgressColumn, Task
    )
    from rich.console import Console
    from rich.live import Live
    from rich.table import Table
    from rich.panel import Panel
    from rich.text import Text
    from rich import box
    _RICH_AVAILABLE = True
except ImportError:
    _RICH_AVAILABLE = False


class OperationStatus(Enum):
    """Status of an operation."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class OperationInfo:
    """Information about a tracked operation."""
    name: str
    description: str
    total_work: Optional[int] = None
    completed_work: int = 0
    status: OperationStatus = OperationStatus.PENDING
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration(self) -> Optional[float]:
        """Get operation duration."""
        if self.start_time is None:
            return None
        end_time = self.end_time or time.time()
        return end_time - self.start_time
    
    @property
    def progress_percentage(self) -> Optional[float]:
        """Get progress percentage."""
        if self.total_work is None or self.total_work == 0:
            return None
        return (self.completed_work / self.total_work) * 100


class AdvancedProgressTracker:
    """Advanced progress tracker with rich visual indicators."""
    
    def __init__(self, console: Optional[Console] = None):
        """Initialize the progress tracker."""
        if not _RICH_AVAILABLE:
            raise ImportError("Rich library is not available. Please install it with: pip install rich")
        
        self.console = console or Console()
        self.operations: Dict[str, OperationInfo] = {}
        self.active_progress: Optional[Progress] = None
        self.task_ids: Dict[str, TaskID] = {}
        self._lock = threading.Lock()
    
    def create_operation(self, operation_id: str, name: str, description: str,
                        total_work: Optional[int] = None, **metadata) -> None:
        """Create a new tracked operation."""
        with self._lock:
            self.operations[operation_id] = OperationInfo(
                name=name,
                description=description,
                total_work=total_work,
                metadata=metadata
            )
    
    def start_operation(self, operation_id: str) -> None:
        """Start tracking an operation."""
        with self._lock:
            if operation_id not in self.operations:
                raise ValueError(f"Operation {operation_id} not found")
            
            operation = self.operations[operation_id]
            operation.status = OperationStatus.RUNNING
            operation.start_time = time.time()
    
    def update_operation(self, operation_id: str, completed_work: int = None,
                        increment: int = None, message: str = None) -> None:
        """Update operation progress."""
        with self._lock:
            if operation_id not in self.operations:
                return
            
            operation = self.operations[operation_id]
            
            if completed_work is not None:
                operation.completed_work = completed_work
            elif increment is not None:
                operation.completed_work += increment
            
            if message:
                operation.description = message
            
            # Update rich progress if active
            if self.active_progress and operation_id in self.task_ids:
                task_id = self.task_ids[operation_id]
                if operation.total_work:
                    self.active_progress.update(
                        task_id,
                        completed=operation.completed_work,
                        description=operation.description
                    )
                else:
                    self.active_progress.update(
                        task_id,
                        description=operation.description
                    )
    
    def complete_operation(self, operation_id: str, message: str = None) -> None:
        """Mark operation as completed."""
        with self._lock:
            if operation_id not in self.operations:
                return
            
            operation = self.operations[operation_id]
            operation.status = OperationStatus.COMPLETED
            operation.end_time = time.time()
            
            if message:
                operation.description = message
            
            # Update rich progress if active
            if self.active_progress and operation_id in self.task_ids:
                task_id = self.task_ids[operation_id]
                if operation.total_work:
                    self.active_progress.update(
                        task_id,
                        completed=operation.total_work,
                        description=operation.description
                    )
    
    def fail_operation(self, operation_id: str, error_message: str) -> None:
        """Mark operation as failed."""
        with self._lock:
            if operation_id not in self.operations:
                return
            
            operation = self.operations[operation_id]
            operation.status = OperationStatus.FAILED
            operation.end_time = time.time()
            operation.error_message = error_message
    
    def cancel_operation(self, operation_id: str) -> None:
        """Cancel an operation."""
        with self._lock:
            if operation_id not in self.operations:
                return
            
            operation = self.operations[operation_id]
            operation.status = OperationStatus.CANCELLED
            operation.end_time = time.time()
    
    @contextmanager
    def progress_context(self, *operation_ids: str, 
                        title: str = "Progress", 
                        show_speed: bool = True) -> Iterator[Progress]:
        """Context manager for rich progress display."""
        columns = [
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
        ]
        
        # Add progress bar for operations with known total work
        has_determinate = any(
            self.operations.get(op_id, OperationInfo("", "")).total_work is not None
            for op_id in operation_ids
        )
        
        if has_determinate:
            columns.extend([
                BarColumn(),
                MofNCompleteColumn(),
                TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            ])
        
        columns.extend([
            TimeElapsedColumn(),
        ])
        
        if has_determinate:
            columns.append(TimeRemainingColumn())
        
        progress = Progress(*columns, console=self.console)
        
        with progress:
            self.active_progress = progress
            
            # Add tasks for each operation
            for op_id in operation_ids:
                if op_id in self.operations:
                    operation = self.operations[op_id]
                    task_id = progress.add_task(
                        operation.description,
                        total=operation.total_work,
                        completed=operation.completed_work
                    )
                    self.task_ids[op_id] = task_id
            
            try:
                yield progress
            finally:
                self.active_progress = None
                self.task_ids.clear()
    
    def print_summary(self, title: str = "Operation Summary") -> None:
        """Print a summary of all operations."""
        if not self.operations:
            self.console.print("[dim]No operations to summarize[/]")
            return
        
        table = Table(title=title, box=box.ROUNDED)
        table.add_column("Operation", style="bold")
        table.add_column("Status", justify="center")
        table.add_column("Progress", justify="center")
        table.add_column("Duration", justify="right")
        table.add_column("Details")
        
        for op_id, operation in self.operations.items():
            # Status with color
            status_colors = {
                OperationStatus.PENDING: "dim",
                OperationStatus.RUNNING: "yellow",
                OperationStatus.COMPLETED: "green",
                OperationStatus.FAILED: "red",
                OperationStatus.CANCELLED: "orange3"
            }
            status_color = status_colors.get(operation.status, "dim")
            status_text = f"[{status_color}]{operation.status.value.title()}[/]"
            
            # Progress
            if operation.total_work:
                progress_pct = operation.progress_percentage or 0
                progress_text = f"{operation.completed_work}/{operation.total_work} ({progress_pct:.1f}%)"
            else:
                progress_text = f"{operation.completed_work}" if operation.completed_work > 0 else "N/A"
            
            # Duration
            duration_text = ""
            if operation.duration:
                duration_text = f"{operation.duration:.1f}s"
            
            # Details
            details = operation.description
            if operation.error_message:
                details += f" | Error: {operation.error_message}"
            
            table.add_row(
                operation.name,
                status_text,
                progress_text,
                duration_text,
                details
            )
        
        self.console.print(table)
    
    def get_operation_status(self, operation_id: str) -> Optional[OperationStatus]:
        """Get the status of an operation."""
        operation = self.operations.get(operation_id)
        return operation.status if operation else None
    
    def get_operation_progress(self, operation_id: str) -> Optional[float]:
        """Get the progress percentage of an operation."""
        operation = self.operations.get(operation_id)
        return operation.progress_percentage if operation else None
    
    def clear_operations(self) -> None:
        """Clear all tracked operations."""
        with self._lock:
            self.operations.clear()
            self.task_ids.clear()


class MultiStepProgressTracker:
    """Progress tracker for multi-step workflows with dependencies."""
    
    def __init__(self, console: Optional[Console] = None):
        """Initialize multi-step progress tracker."""
        self.tracker = AdvancedProgressTracker(console)
        self.console = console or Console()
        self.steps: List[Dict[str, Any]] = []
        self.current_step_index = 0
        self.workflow_start_time: Optional[float] = None
    
    def add_step(self, step_id: str, name: str, description: str,
                estimated_duration: Optional[float] = None,
                total_work: Optional[int] = None) -> None:
        """Add a step to the workflow."""
        self.steps.append({
            'id': step_id,
            'name': name,
            'description': description,
            'estimated_duration': estimated_duration,
            'total_work': total_work,
            'index': len(self.steps)
        })
        
        self.tracker.create_operation(
            step_id, name, description, total_work,
            estimated_duration=estimated_duration
        )
    
    def start_workflow(self, title: str = "Multi-Step Workflow") -> None:
        """Start the multi-step workflow."""
        self.workflow_start_time = time.time()
        
        # Print workflow overview
        self.console.print(f"\n[bold blue]{title}[/]")
        self.console.print(f"[dim]Total steps: {len(self.steps)}[/]\n")
        
        self._print_step_overview()
    
    def start_next_step(self) -> Optional[str]:
        """Start the next step in the workflow."""
        if self.current_step_index >= len(self.steps):
            return None
        
        step = self.steps[self.current_step_index]
        step_id = step['id']
        
        self.console.print(f"\n[blue]Step {self.current_step_index + 1}/{len(self.steps)}:[/] {step['name']}")
        self.tracker.start_operation(step_id)
        
        return step_id
    
    def complete_current_step(self, message: str = None) -> None:
        """Complete the current step."""
        if self.current_step_index >= len(self.steps):
            return
        
        step = self.steps[self.current_step_index]
        self.tracker.complete_operation(step['id'], message)
        self.current_step_index += 1
        
        self.console.print(f"[green]✓ Completed: {step['name']}[/]")
    
    def fail_current_step(self, error_message: str) -> None:
        """Fail the current step."""
        if self.current_step_index >= len(self.steps):
            return
        
        step = self.steps[self.current_step_index]
        self.tracker.fail_operation(step['id'], error_message)
        
        self.console.print(f"[red]✗ Failed: {step['name']} - {error_message}[/]")
    
    def update_current_step(self, completed_work: int = None, 
                           increment: int = None, message: str = None) -> None:
        """Update the current step progress."""
        if self.current_step_index >= len(self.steps):
            return
        
        step = self.steps[self.current_step_index]
        self.tracker.update_operation(step['id'], completed_work, increment, message)
    
    def _print_step_overview(self) -> None:
        """Print an overview of all steps."""
        table = Table(title="Workflow Steps", box=box.SIMPLE)
        table.add_column("Step", style="bold", width=4)
        table.add_column("Name", style="blue")
        table.add_column("Description")
        table.add_column("Est. Duration", justify="right")
        
        for i, step in enumerate(self.steps):
            duration = f"{step['estimated_duration']:.1f}s" if step['estimated_duration'] else "Unknown"
            table.add_row(
                str(i + 1),
                step['name'],
                step['description'],
                duration
            )
        
        self.console.print(table)
    
    def print_workflow_summary(self) -> None:
        """Print workflow completion summary."""
        if not self.workflow_start_time:
            return
        
        total_duration = time.time() - self.workflow_start_time
        completed_steps = sum(
            1 for step in self.steps
            if self.tracker.get_operation_status(step['id']) == OperationStatus.COMPLETED
        )
        failed_steps = sum(
            1 for step in self.steps
            if self.tracker.get_operation_status(step['id']) == OperationStatus.FAILED
        )
        
        # Create summary panel
        summary_lines = [
            f"[bold]Total Steps:[/] {len(self.steps)}",
            f"[bold]Completed:[/] [green]{completed_steps}[/]",
            f"[bold]Failed:[/] [red]{failed_steps}[/]",
            f"[bold]Total Duration:[/] {total_duration:.1f}s"
        ]
        
        if failed_steps == 0:
            title = "[green]Workflow Completed Successfully[/]"
            border_style = "green"
        else:
            title = "[red]Workflow Completed with Errors[/]"
            border_style = "red"
        
        panel = Panel(
            "\n".join(summary_lines),
            title=title,
            border_style=border_style,
            box=box.ROUNDED
        )
        
        self.console.print(panel)
        
        # Print detailed operation summary
        self.tracker.print_summary("Detailed Step Summary")
