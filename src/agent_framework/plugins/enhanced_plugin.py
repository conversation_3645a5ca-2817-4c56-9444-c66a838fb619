"""
Enhanced plugin base class with hot reload and function exposure support.

Provides an improved plugin base class that supports hot reload lifecycle events,
automatic function exposure, and seamless model integration.
"""

import asyncio
import inspect
import logging
from typing import Any, Dict, List, Optional, Callable
from abc import abstractmethod

from ..core.types import PluginInterface, PluginRequest, PluginResponse, PluginCapability
from .decorators import is_exposed_function, extract_function_metadata
from .function_registry import FunctionParameter, ParameterType


class EnhancedPlugin(PluginInterface):
    """
    Enhanced plugin base class with hot reload and function exposure support.
    
    Provides automatic function discovery, hot reload lifecycle events,
    and improved integration with the agent framework.
    """
    
    def __init__(self):
        """Initialize the enhanced plugin."""
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self._is_initialized = False
        self._config: Dict[str, Any] = {}
        self._exposed_functions: Dict[str, Callable] = {}
        
        # Hot reload support
        self._reload_count = 0
        self._last_reload_time: Optional[float] = None
        
        # Automatically discover exposed functions
        self._discover_exposed_functions()
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Get the plugin name."""
        pass
    
    @property
    def version(self) -> str:
        """Get the plugin version."""
        return getattr(self, 'PLUGIN_VERSION', '1.0.0')
    
    @property
    def description(self) -> str:
        """Get the plugin description."""
        return getattr(self, 'PLUGIN_DESCRIPTION', self.__doc__ or '')
    
    @property
    def author(self) -> str:
        """Get the plugin author."""
        return getattr(self, 'PLUGIN_AUTHOR', '')
    
    @property
    def license(self) -> str:
        """Get the plugin license."""
        return getattr(self, 'PLUGIN_LICENSE', '')
    
    @property
    def dependencies(self) -> List[str]:
        """Get the plugin dependencies."""
        return getattr(self, 'PLUGIN_DEPENDENCIES', [])
    
    @property
    def exposed_functions(self) -> Dict[str, Callable]:
        """Get exposed functions."""
        return self._exposed_functions.copy()
    
    @property
    def reload_count(self) -> int:
        """Get the number of times this plugin has been reloaded."""
        return self._reload_count
    
    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin."""
        if self._is_initialized:
            self.logger.warning(f"Plugin {self.name} already initialized")
            return
        
        self._config = config.copy()
        
        try:
            # Call plugin-specific initialization
            await self._initialize_plugin(config)
            
            # Set up exposed functions
            await self._setup_exposed_functions()
            
            self._is_initialized = True
            self.logger.info(f"Enhanced plugin {self.name} initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize plugin {self.name}: {e}")
            raise
    
    async def cleanup(self) -> None:
        """Clean up plugin resources."""
        if not self._is_initialized:
            return
        
        try:
            # Call plugin-specific cleanup
            await self._cleanup_plugin()
            
            # Clear exposed functions
            self._exposed_functions.clear()
            
            self._is_initialized = False
            self.logger.info(f"Enhanced plugin {self.name} cleaned up successfully")
            
        except Exception as e:
            self.logger.error(f"Error cleaning up plugin {self.name}: {e}")
            raise
    
    async def execute(self, request: PluginRequest) -> PluginResponse:
        """Execute a plugin request."""
        if not self._is_initialized:
            return PluginResponse(
                success=False,
                error=f"Plugin {self.name} not initialized"
            )
        
        try:
            # Check if this is a function call request
            if request.capability in self._exposed_functions:
                return await self._execute_exposed_function(request)
            
            # Fall back to capability-based execution
            return await self._execute_capability(request)
            
        except Exception as e:
            self.logger.error(f"Error executing request in plugin {self.name}: {e}")
            return PluginResponse(
                success=False,
                error=str(e),
                metadata={
                    "plugin_name": self.name,
                    "capability": request.capability
                }
            )
    
    async def get_capabilities(self) -> List[PluginCapability]:
        """Get the capabilities provided by this plugin."""
        capabilities = []
        
        # Add capabilities from exposed functions
        for func_name, func in self._exposed_functions.items():
            metadata = extract_function_metadata(func)
            
            # Create capability from function metadata
            capability = PluginCapability(
                name=func_name,
                description=metadata.get('description', func.__doc__ or ''),
                input_schema=self._build_input_schema(metadata.get('parameters', [])),
                output_schema=self._build_output_schema(metadata.get('return_metadata', {}))
            )
            capabilities.append(capability)
        
        # Add plugin-specific capabilities
        plugin_capabilities = await self._get_plugin_capabilities()
        capabilities.extend(plugin_capabilities)
        
        return capabilities
    
    # Hot reload lifecycle methods
    
    async def on_before_reload(self) -> None:
        """Called before the plugin is reloaded."""
        self.logger.info(f"Plugin {self.name} preparing for reload")
        
        # Save any state that should persist across reloads
        await self._save_reload_state()
    
    async def on_after_reload(self) -> None:
        """Called after the plugin has been reloaded."""
        import time
        
        self._reload_count += 1
        self._last_reload_time = time.time()
        
        self.logger.info(f"Plugin {self.name} reloaded (count: {self._reload_count})")
        
        # Restore any saved state
        await self._restore_reload_state()
        
        # Re-discover exposed functions
        self._discover_exposed_functions()
        await self._setup_exposed_functions()
    
    # Abstract methods for subclasses
    
    @abstractmethod
    async def _initialize_plugin(self, config: Dict[str, Any]) -> None:
        """Plugin-specific initialization. Override in subclasses."""
        pass
    
    @abstractmethod
    async def _cleanup_plugin(self) -> None:
        """Plugin-specific cleanup. Override in subclasses."""
        pass
    
    @abstractmethod
    async def _execute_capability(self, request: PluginRequest) -> PluginResponse:
        """Execute a capability request. Override in subclasses."""
        pass
    
    async def _get_plugin_capabilities(self) -> List[PluginCapability]:
        """Get plugin-specific capabilities. Override in subclasses if needed."""
        return []
    
    # Hot reload state management (override if needed)
    
    async def _save_reload_state(self) -> None:
        """Save state before reload. Override in subclasses if needed."""
        pass
    
    async def _restore_reload_state(self) -> None:
        """Restore state after reload. Override in subclasses if needed."""
        pass
    
    # Internal methods
    
    def _discover_exposed_functions(self) -> None:
        """Discover exposed functions in the plugin."""
        self._exposed_functions.clear()
        
        for attr_name in dir(self):
            if attr_name.startswith('_'):
                continue
            
            attr = getattr(self, attr_name)
            if callable(attr) and is_exposed_function(attr):
                self._exposed_functions[attr_name] = attr
                self.logger.debug(f"Discovered exposed function: {attr_name}")
    
    async def _setup_exposed_functions(self) -> None:
        """Set up exposed functions for execution."""
        for func_name, func in self._exposed_functions.items():
            # Validate function metadata
            try:
                metadata = extract_function_metadata(func)
                self.logger.debug(f"Set up exposed function {func_name} with metadata: {metadata}")
            except Exception as e:
                self.logger.warning(f"Invalid metadata for function {func_name}: {e}")
    
    async def _execute_exposed_function(self, request: PluginRequest) -> PluginResponse:
        """Execute an exposed function."""
        import time
        
        func_name = request.capability
        func = self._exposed_functions[func_name]
        
        start_time = time.time()
        
        try:
            # Prepare arguments
            args = request.parameters
            
            # Call the function
            if inspect.iscoroutinefunction(func):
                result = await func(**args)
            else:
                result = func(**args)
            
            execution_time = time.time() - start_time
            
            return PluginResponse(
                success=True,
                result=result,
                execution_time=execution_time,
                metadata={
                    "plugin_name": self.name,
                    "function_name": func_name,
                    "execution_type": "exposed_function"
                }
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            return PluginResponse(
                success=False,
                error=str(e),
                execution_time=execution_time,
                metadata={
                    "plugin_name": self.name,
                    "function_name": func_name,
                    "execution_type": "exposed_function"
                }
            )
    
    def _build_input_schema(self, parameters: List[FunctionParameter]) -> Dict[str, Any]:
        """Build input schema from function parameters."""
        properties = {}
        required = []
        
        for param in parameters:
            param_schema = {
                "type": param.type.value,
                "description": param.description
            }
            
            if param.schema:
                param_schema.update(param.schema)
            
            if param.examples:
                param_schema["examples"] = param.examples
            
            if param.default is not None:
                param_schema["default"] = param.default
            
            properties[param.name] = param_schema
            
            if param.required:
                required.append(param.name)
        
        return {
            "type": "object",
            "properties": properties,
            "required": required
        }
    
    def _build_output_schema(self, return_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """Build output schema from return metadata."""
        if not return_metadata:
            return {"type": "any"}
        
        schema = {
            "type": return_metadata.get('type', {}).value if hasattr(return_metadata.get('type', {}), 'value') else "any",
            "description": return_metadata.get('description', '')
        }
        
        if 'schema' in return_metadata:
            schema.update(return_metadata['schema'])
        
        return schema
