"""
Plugin system for the agent framework.

Provides dynamic loading, registration, and management of plugins
that extend the framework's functionality. Enhanced with hot reload
capabilities, function exposure, and AI model integration.
"""

from .manager import PluginManager
from .loader import PluginLoader
from .registry import PluginRegistry
from .hot_reload import HotReloadManager, HotReloadConfig, ReloadEvent
from .function_registry import FunctionRegistry, FunctionMetadata, FunctionCallResult, ParameterType
from .model_integration import ModelIntegration, ModelFunction, FunctionCallRequest
from .enhanced_plugin import EnhancedPlugin
from .decorators import (
    exposed_function, parameter, returns, example,
    string_param, int_param, float_param, bool_param, array_param, object_param,
    validate_input, validate_output, cached, rate_limited,
    extract_function_metadata, is_exposed_function
)

__all__ = [
    # Core components
    "PluginManager",
    "PluginLoader",
    "PluginRegistry",

    # Hot reload system
    "HotReloadManager",
    "HotReloadConfig",
    "ReloadEvent",

    # Function registry
    "FunctionRegistry",
    "FunctionMetadata",
    "FunctionCallResult",
    "ParameterType",

    # Model integration
    "ModelIntegration",
    "ModelFunction",
    "FunctionCallRequest",

    # Enhanced plugin base
    "EnhancedPlugin",

    # Decorators and utilities
    "exposed_function",
    "parameter",
    "returns",
    "example",
    "string_param",
    "int_param",
    "float_param",
    "bool_param",
    "array_param",
    "object_param",
    "validate_input",
    "validate_output",
    "cached",
    "rate_limited",
    "extract_function_metadata",
    "is_exposed_function",
]