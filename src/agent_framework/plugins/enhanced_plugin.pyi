"""
Enhanced plugin base class with advanced features.
"""

from typing import Any, Dict, List, Optional, Callable, Awaitable
from abc import ABC, abstractmethod
from ..core.types import PluginInterface
from .function_registry import FunctionRegistry, FunctionMetadata

class EnhancedPlugin(PluginInterface):
    """
    Enhanced plugin base class with advanced features.
    
    Provides:
    - Hot reload capability
    - Function exposure and discovery
    - AI model integration
    - Automatic validation
    - Caching and rate limiting
    - Comprehensive error handling
    """
    
    def __init__(self) -> None: ...
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Plugin name."""
        ...
    
    @property
    @abstractmethod
    def version(self) -> str:
        """Plugin version."""
        ...
    
    @property
    def description(self) -> str:
        """Plugin description."""
        ...
    
    @property
    def author(self) -> str:
        """Plugin author."""
        ...
    
    @property
    def dependencies(self) -> List[str]:
        """Plugin dependencies."""
        ...
    
    async def initialize(self) -> None:
        """Initialize the plugin."""
        ...
    
    async def shutdown(self) -> None:
        """Shutdown the plugin."""
        ...
    
    async def get_capabilities(self) -> List[str]:
        """Get plugin capabilities."""
        ...
    
    async def reload(self) -> None:
        """Reload the plugin."""
        ...
    
    def get_exposed_functions(self) -> Dict[str, FunctionMetadata]:
        """Get all exposed functions."""
        ...
    
    async def call_function(self, function_name: str, **kwargs: Any) -> Any:
        """Call an exposed function."""
        ...
    
    def get_function_metadata(self, function_name: str) -> Optional[FunctionMetadata]:
        """Get metadata for a specific function."""
        ...
    
    async def validate_function_input(self, function_name: str, **kwargs: Any) -> bool:
        """Validate function input."""
        ...
    
    async def validate_function_output(self, function_name: str, result: Any) -> bool:
        """Validate function output."""
        ...
    
    def add_function_hook(self, 
                         function_name: str,
                         hook_type: str,
                         hook_func: Callable[[Any], Awaitable[Any]]) -> None:
        """Add a function hook."""
        ...
    
    def remove_function_hook(self, 
                            function_name: str,
                            hook_type: str,
                            hook_func: Callable[[Any], Awaitable[Any]]) -> None:
        """Remove a function hook."""
        ...
    
    async def get_plugin_metrics(self) -> Dict[str, Any]:
        """Get plugin metrics."""
        ...
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform plugin health check."""
        ...
    
    def get_configuration_schema(self) -> Dict[str, Any]:
        """Get plugin configuration schema."""
        ...
    
    async def configure(self, config: Dict[str, Any]) -> None:
        """Configure the plugin."""
        ...
    
    # Properties
    @property
    def function_registry(self) -> FunctionRegistry: ...
    
    @property
    def is_initialized(self) -> bool: ...
    
    @property
    def is_hot_reloadable(self) -> bool: ...
    
    @property
    def supports_ai_integration(self) -> bool: ...
