"""
Decorators and utilities for plugin function exposure.

Provides easy-to-use decorators for plugin developers to expose functions
with proper metadata, validation, and documentation for AI model integration.
"""

import functools
import inspect
import logging
from typing import Any, Callable, Dict, List, Optional, Type, TypeVar, Union

from .function_registry import FunctionParameter, ParameterType

# Type variable for decorated functions
F = TypeVar('F', bound=Callable[..., Any])


def exposed_function(
    name: Optional[str] = None,
    description: Optional[str] = None,
    parameters: Optional[List[FunctionParameter]] = None,
    return_type: ParameterType = ParameterType.ANY,
    return_description: str = "",
    examples: Optional[List[Dict[str, Any]]] = None,
    tags: Optional[List[str]] = None,
    category: Optional[str] = None,
    aliases: Optional[List[str]] = None,
    deprecated: bool = False,
    version: str = "1.0.0"
) -> Callable[[F], F]:
    """
    Decorator to expose a plugin function to the AI model.
    
    Args:
        name: Function name (defaults to function.__name__)
        description: Function description (defaults to docstring)
        parameters: Parameter metadata (auto-extracted if not provided)
        return_type: Return value type
        return_description: Description of return value
        examples: Usage examples
        tags: Function tags for categorization
        category: Function category
        aliases: Alternative names for the function
        deprecated: Whether the function is deprecated
        version: Function version
        
    Returns:
        Decorated function with exposure metadata
    """
    def decorator(func: F) -> F:
        # Store metadata on the function
        func._exposed_function_metadata = {
            'name': name,
            'description': description,
            'parameters': parameters,
            'return_type': return_type,
            'return_description': return_description,
            'examples': examples or [],
            'tags': tags or [],
            'category': category,
            'aliases': aliases or [],
            'deprecated': deprecated,
            'version': version
        }
        
        # Mark as exposed
        func._is_exposed_function = True
        
        return func
    
    return decorator


def parameter(
    name: str,
    type: Union[ParameterType, Type, str],
    description: str = "",
    required: bool = True,
    default: Any = None,
    examples: Optional[List[Any]] = None,
    schema: Optional[Dict[str, Any]] = None
) -> Callable[[F], F]:
    """
    Decorator to add parameter metadata to a function.
    
    Can be used multiple times to define multiple parameters.
    
    Args:
        name: Parameter name
        type: Parameter type
        description: Parameter description
        required: Whether parameter is required
        default: Default value
        examples: Example values
        schema: JSON schema for validation
        
    Returns:
        Decorated function with parameter metadata
    """
    def decorator(func: F) -> F:
        if not hasattr(func, '_parameter_metadata'):
            func._parameter_metadata = []
        
        # Convert type to ParameterType if needed
        param_type = type
        if isinstance(type, str):
            param_type = ParameterType(type)
        elif isinstance(type, Type):
            param_type = _type_to_parameter_type(type)
        
        param = FunctionParameter(
            name=name,
            type=param_type,
            description=description,
            required=required,
            default=default,
            examples=examples or [],
            schema=schema
        )
        
        func._parameter_metadata.append(param)
        return func
    
    return decorator


def returns(
    type: Union[ParameterType, Type, str],
    description: str = "",
    schema: Optional[Dict[str, Any]] = None
) -> Callable[[F], F]:
    """
    Decorator to specify return type metadata.
    
    Args:
        type: Return type
        description: Return value description
        schema: JSON schema for return value
        
    Returns:
        Decorated function with return metadata
    """
    def decorator(func: F) -> F:
        # Convert type to ParameterType if needed
        return_type = type
        if isinstance(type, str):
            return_type = ParameterType(type)
        elif isinstance(type, Type):
            return_type = _type_to_parameter_type(type)
        
        func._return_metadata = {
            'type': return_type,
            'description': description,
            'schema': schema
        }
        
        return func
    
    return decorator


def example(
    description: str,
    input: Dict[str, Any],
    output: Any,
    notes: Optional[str] = None
) -> Callable[[F], F]:
    """
    Decorator to add usage examples to a function.
    
    Can be used multiple times to add multiple examples.
    
    Args:
        description: Example description
        input: Example input parameters
        output: Example output
        notes: Additional notes
        
    Returns:
        Decorated function with example metadata
    """
    def decorator(func: F) -> F:
        if not hasattr(func, '_example_metadata'):
            func._example_metadata = []
        
        example_data = {
            'description': description,
            'input': input,
            'output': output,
            'notes': notes
        }
        
        func._example_metadata.append(example_data)
        return func
    
    return decorator


def validate_input(validator: Callable[[Dict[str, Any]], Optional[str]]) -> Callable[[F], F]:
    """
    Decorator to add custom input validation.
    
    Args:
        validator: Function that takes input dict and returns error string or None
        
    Returns:
        Decorated function with input validation
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Validate input if called with keyword arguments
            if kwargs:
                error = validator(kwargs)
                if error:
                    raise ValueError(f"Input validation failed: {error}")
            
            return func(*args, **kwargs)
        
        # Store validator for registry use
        wrapper._input_validator = validator
        
        # Copy over any existing metadata
        for attr in dir(func):
            if attr.startswith('_') and hasattr(func, attr):
                setattr(wrapper, attr, getattr(func, attr))
        
        return wrapper
    
    return decorator


def validate_output(validator: Callable[[Any], Optional[str]]) -> Callable[[F], F]:
    """
    Decorator to add custom output validation.
    
    Args:
        validator: Function that takes output and returns error string or None
        
    Returns:
        Decorated function with output validation
    """
    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            result = func(*args, **kwargs)
            
            # Validate output
            error = validator(result)
            if error:
                raise ValueError(f"Output validation failed: {error}")
            
            return result
        
        # Store validator for registry use
        wrapper._output_validator = validator
        
        # Copy over any existing metadata
        for attr in dir(func):
            if attr.startswith('_') and hasattr(func, attr):
                setattr(wrapper, attr, getattr(func, attr))
        
        return wrapper
    
    return decorator


def cached(ttl: int = 300, key_func: Optional[Callable[..., str]] = None) -> Callable[[F], F]:
    """
    Decorator to cache function results.
    
    Args:
        ttl: Time to live in seconds
        key_func: Function to generate cache key from arguments
        
    Returns:
        Decorated function with caching
    """
    def decorator(func: F) -> F:
        cache = {}
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            import time
            
            # Generate cache key
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = str(hash((args, tuple(sorted(kwargs.items())))))
            
            # Check cache
            if cache_key in cache:
                result, timestamp = cache[cache_key]
                if time.time() - timestamp < ttl:
                    return result
                else:
                    del cache[cache_key]
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache[cache_key] = (result, time.time())
            
            return result
        
        # Store cache metadata
        wrapper._cache_ttl = ttl
        wrapper._cache_key_func = key_func
        
        # Copy over any existing metadata
        for attr in dir(func):
            if attr.startswith('_') and hasattr(func, attr):
                setattr(wrapper, attr, getattr(func, attr))
        
        return wrapper
    
    return decorator


def rate_limited(calls_per_minute: int = 60) -> Callable[[F], F]:
    """
    Decorator to rate limit function calls.
    
    Args:
        calls_per_minute: Maximum calls per minute
        
    Returns:
        Decorated function with rate limiting
    """
    def decorator(func: F) -> F:
        call_times = []
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            import time
            
            current_time = time.time()
            
            # Remove old call times
            call_times[:] = [t for t in call_times if current_time - t < 60]
            
            # Check rate limit
            if len(call_times) >= calls_per_minute:
                raise RuntimeError(f"Rate limit exceeded: {calls_per_minute} calls per minute")
            
            # Record call time
            call_times.append(current_time)
            
            return func(*args, **kwargs)
        
        # Store rate limit metadata
        wrapper._rate_limit = calls_per_minute
        
        # Copy over any existing metadata
        for attr in dir(func):
            if attr.startswith('_') and hasattr(func, attr):
                setattr(wrapper, attr, getattr(func, attr))
        
        return wrapper
    
    return decorator


def extract_function_metadata(func: Callable) -> Dict[str, Any]:
    """
    Extract all metadata from a decorated function.
    
    Args:
        func: Function to extract metadata from
        
    Returns:
        Dictionary containing all function metadata
    """
    metadata = {}
    
    # Basic function info
    metadata['name'] = getattr(func, '__name__', 'unknown')
    metadata['doc'] = getattr(func, '__doc__', '')
    
    # Exposed function metadata
    if hasattr(func, '_exposed_function_metadata'):
        metadata.update(func._exposed_function_metadata)
    
    # Parameter metadata
    if hasattr(func, '_parameter_metadata'):
        metadata['parameters'] = func._parameter_metadata
    
    # Return metadata
    if hasattr(func, '_return_metadata'):
        metadata['return_metadata'] = func._return_metadata
    
    # Example metadata
    if hasattr(func, '_example_metadata'):
        metadata['examples'] = func._example_metadata
    
    # Validation metadata
    if hasattr(func, '_input_validator'):
        metadata['input_validator'] = func._input_validator
    
    if hasattr(func, '_output_validator'):
        metadata['output_validator'] = func._output_validator
    
    # Cache metadata
    if hasattr(func, '_cache_ttl'):
        metadata['cache_ttl'] = func._cache_ttl
        metadata['cache_key_func'] = getattr(func, '_cache_key_func', None)
    
    # Rate limit metadata
    if hasattr(func, '_rate_limit'):
        metadata['rate_limit'] = func._rate_limit
    
    return metadata


def is_exposed_function(func: Callable) -> bool:
    """
    Check if a function is marked as exposed.
    
    Args:
        func: Function to check
        
    Returns:
        True if function is exposed, False otherwise
    """
    return getattr(func, '_is_exposed_function', False)


def _type_to_parameter_type(type_hint: Type) -> ParameterType:
    """Convert Python type to ParameterType."""
    if type_hint == str:
        return ParameterType.STRING
    elif type_hint == int:
        return ParameterType.INTEGER
    elif type_hint == float:
        return ParameterType.FLOAT
    elif type_hint == bool:
        return ParameterType.BOOLEAN
    elif hasattr(type_hint, '__origin__'):
        if type_hint.__origin__ == list:
            return ParameterType.ARRAY
        elif type_hint.__origin__ == dict:
            return ParameterType.OBJECT
    
    return ParameterType.ANY


# Convenience decorators for common patterns
def string_param(name: str, description: str = "", required: bool = True, default: str = None):
    """Convenience decorator for string parameters."""
    return parameter(name, ParameterType.STRING, description, required, default)


def int_param(name: str, description: str = "", required: bool = True, default: int = None):
    """Convenience decorator for integer parameters."""
    return parameter(name, ParameterType.INTEGER, description, required, default)


def float_param(name: str, description: str = "", required: bool = True, default: float = None):
    """Convenience decorator for float parameters."""
    return parameter(name, ParameterType.FLOAT, description, required, default)


def bool_param(name: str, description: str = "", required: bool = True, default: bool = None):
    """Convenience decorator for boolean parameters."""
    return parameter(name, ParameterType.BOOLEAN, description, required, default)


def array_param(name: str, description: str = "", required: bool = True, default: list = None):
    """Convenience decorator for array parameters."""
    return parameter(name, ParameterType.ARRAY, description, required, default)


def object_param(name: str, description: str = "", required: bool = True, default: dict = None):
    """Convenience decorator for object parameters."""
    return parameter(name, ParameterType.OBJECT, description, required, default)
