"""
Plugin manager for coordinating plugin lifecycle and operations.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Awaitable, Sequence, cast

from ..core.config import FrameworkConfig
from ..core.types import PluginInterface, PluginRequest, PluginResponse, PluginCapability
from .registry import PluginRegistry, PluginMetadata
from .loader import <PERSON>lugin<PERSON>oader
from .hot_reload import HotReloadManager, HotReloadConfig
from .function_registry import FunctionRegistry, FunctionCallResult


class PluginManager:
    """
    Central manager for plugin lifecycle and operations.

    Coordinates plugin discovery, loading, execution, and management
    across the entire framework.
    """

    def __init__(self, config: FrameworkConfig, message_broker=None, hot_reload_config: Optional[HotReloadConfig] = None):
        """Initialize the plugin manager."""
        self.config = config
        self.message_broker = message_broker
        self.logger = logging.getLogger(__name__)

        # Core components
        self.registry = PluginRegistry()
        self.loader = PluginLoader(
            self.registry,
            self.config.plugins.allowed_imports
        )

        # Hot reload system
        self.hot_reload_manager = HotReloadManager(
            self,
            hot_reload_config or HotReloadConfig()
        )

        # State
        self._is_initialized = False

    async def initialize(self) -> None:
        """Initialize the plugin manager."""
        if self._is_initialized:
            return

        self.logger.info("Initializing plugin manager...")

        try:
            # Discover plugins in configured directories
            await self.discover_plugins()

            # Load enabled plugins if auto-load is enabled
            if self.config.plugins.auto_load_plugins:
                await self.load_all_plugins()

            # Start hot reload system
            await self.hot_reload_manager.start()

            self._is_initialized = True
            self.logger.info("Plugin manager initialized successfully")

        except Exception as e:
            self.logger.error(f"Failed to initialize plugin manager: {e}")
            raise

    async def discover_plugins(self) -> None:
        """Discover plugins in configured directories."""
        self.logger.info("Discovering plugins...")

        discovered = await self.loader.discover_plugins(
            self.config.plugins.plugin_directories
        )

        # Register discovered plugins
        for metadata in discovered:
            try:
                self.registry.register_plugin(metadata)
            except Exception as e:
                self.logger.error(f"Failed to register plugin {metadata.name}: {e}")

        stats = self.registry.get_registry_stats()
        self.logger.info(f"Discovery complete: {stats['total_plugins']} plugins found")

    async def load_plugin(self, plugin_name: str, config: Optional[Dict[str, Any]] = None) -> PluginInterface:
        """Load a specific plugin."""
        return await self.loader.load_plugin(plugin_name, config)

    async def load_all_plugins(self) -> None:
        """Load all enabled plugins."""
        enabled_plugins = self.registry.get_enabled_plugins()

        if not enabled_plugins:
            self.logger.info("No enabled plugins to load")
            return

        # Get plugins in dependency order
        plugin_names = list(enabled_plugins.keys())
        ordered_names = self.registry.get_dependency_order(plugin_names)

        self.logger.info(f"Loading {len(ordered_names)} plugins in dependency order")

        for plugin_name in ordered_names:
            try:
                await self.load_plugin(plugin_name)
            except Exception as e:
                self.logger.error(f"Failed to load plugin {plugin_name}: {e}")
                # Continue loading other plugins

    async def unload_plugin(self, plugin_name: str) -> None:
        """Unload a specific plugin."""
        await self.loader.unload_plugin(plugin_name)

    async def reload_plugin(self, plugin_name: str, config: Optional[Dict[str, Any]] = None) -> PluginInterface:
        """Reload a plugin."""
        return await self.loader.reload_plugin(plugin_name, config)

    async def execute_plugin_request(self, plugin_name: str, request: PluginRequest) -> PluginResponse:
        """Execute a request on a specific plugin."""
        if not self.loader.is_plugin_loaded(plugin_name):
            # Try to load the plugin if not loaded
            await self.load_plugin(plugin_name)

        plugin_instance = self.loader.get_loaded_plugins().get(plugin_name)
        if not plugin_instance:
            raise ValueError(f"Plugin not available: {plugin_name}")

        try:
            # Apply timeout if specified
            if request.timeout_seconds:
                response = await asyncio.wait_for(
                    plugin_instance.execute(request),
                    timeout=request.timeout_seconds
                )
            else:
                response = await plugin_instance.execute(request)

            return response

        except asyncio.TimeoutError:
            return PluginResponse(
                success=False,
                error=f"Plugin execution timed out after {request.timeout_seconds}s"
            )
        except Exception as e:
            return PluginResponse(
                success=False,
                error=str(e)
            )

    async def get_plugin_capabilities(self, plugin_name: str) -> List[PluginCapability]:
        """Get capabilities for a specific plugin."""
        if not self.loader.is_plugin_loaded(plugin_name):
            await self.load_plugin(plugin_name)

        plugin_instance = self.loader.get_loaded_plugins().get(plugin_name)
        if not plugin_instance:
            raise ValueError(f"Plugin not available: {plugin_name}")

        return await plugin_instance.get_capabilities()

    async def get_all_capabilities(self) -> Dict[str, List[PluginCapability]]:
        """Get capabilities for all loaded plugins."""
        capabilities: Dict[str, List[PluginCapability]] = {}

        for plugin_name, plugin_instance in self.loader.get_loaded_plugins().items():
            try:
                capabilities[plugin_name] = await plugin_instance.get_capabilities()
            except Exception as e:
                self.logger.error(f"Failed to get capabilities for {plugin_name}: {e}")
                capabilities[plugin_name] = []

        return capabilities

    async def find_plugins_by_capability(self, capability_name: str) -> List[str]:
        """Find plugins that provide a specific capability."""
        matching_plugins: List[str] = []

        for plugin_name, plugin_instance in self.loader.get_loaded_plugins().items():
            try:
                capabilities = await plugin_instance.get_capabilities()
                for capability in capabilities:
                    if capability.name == capability_name:
                        matching_plugins.append(plugin_name)
                        break
            except Exception as e:
                self.logger.error(f"Error checking capabilities for {plugin_name}: {e}")

        return matching_plugins

    async def get_all_tools(self) -> List[Any]:
        """Get all tools from loaded plugins for agent integration."""
        tools: List[Any] = []

        for plugin_name, plugin_instance in self.loader.get_loaded_plugins().items():
            try:
                # Check if plugin has tools method
                if hasattr(plugin_instance, "get_tools"):
                    # Help type checker: getattr returns a callable returning Awaitable[Sequence[Any]] or List[Any]
                    get_tools_fn = cast(Any, getattr(plugin_instance, "get_tools"))
                    plugin_tools = await get_tools_fn()
                    if plugin_tools:
                        tools.extend(list(plugin_tools))
                        self.logger.debug(f"Added {len(plugin_tools)} tools from {plugin_name}")
            except Exception as e:
                self.logger.error(f"Error getting tools from {plugin_name}: {e}")

        self.logger.info(f"Collected {len(tools)} tools from plugins")
        return tools

    def get_loaded_plugins(self) -> Dict[str, PluginInterface]:
        """Get all currently loaded plugins."""
        return self.loader.get_loaded_plugins()

    def get_plugin_registry(self) -> PluginRegistry:
        """Get the plugin registry."""
        return self.registry

    def get_plugin_metadata(self, plugin_name: str) -> Optional[PluginMetadata]:
        """Get metadata for a specific plugin."""
        return self.registry.get_plugin(plugin_name)

    def enable_plugin(self, plugin_name: str) -> None:
        """Enable a plugin."""
        self.registry.enable_plugin(plugin_name)

    def disable_plugin(self, plugin_name: str) -> None:
        """Disable a plugin."""
        self.registry.disable_plugin(plugin_name)

    async def get_plugin_status(self) -> Dict[str, Any]:
        """Get comprehensive plugin status information."""
        registry_stats = self.registry.get_registry_stats()
        loaded_plugins = self.loader.get_loaded_plugins()

        plugin_details: Dict[str, Any] = {}
        for name, metadata in self.registry.get_all_plugins().items():
            plugin_details[name] = {
                "metadata": metadata,
                "loaded": name in loaded_plugins,
                "enabled": metadata.is_enabled,
                "load_count": metadata.load_count,
                "error_count": metadata.error_count,
                "last_loaded": metadata.last_loaded
            }

        return {
            "registry_stats": registry_stats,
            "loaded_count": len(loaded_plugins),
            "plugin_details": plugin_details
        }

    async def shutdown(self) -> None:
        """Shutdown the plugin manager and cleanup resources."""
        if not self._is_initialized:
            return

        self.logger.info("Shutting down plugin manager...")

        try:
            # Stop hot reload system
            await self.hot_reload_manager.stop()

            # Cleanup all loaded plugins
            await self.loader.cleanup_all()

            # Clear registry
            self.registry.clear()

            self._is_initialized = False
            self.logger.info("Plugin manager shutdown complete")

        except Exception as e:
            self.logger.error(f"Error during plugin manager shutdown: {e}")

    @property
    def is_initialized(self) -> bool:
        """Check if the plugin manager is initialized."""
        return self._is_initialized

    # Function calling interface

    async def call_plugin_function(self,
                                 function_name: str,
                                 arguments: Optional[Dict[str, Any]] = None,
                                 validate_args: bool = True) -> FunctionCallResult:
        """
        Call a plugin function by name.

        Args:
            function_name: Name of the function to call
            arguments: Function arguments
            validate_args: Whether to validate arguments

        Returns:
            Function call result
        """
        return await self.loader.function_registry.call_function(
            function_name, arguments, validate_args
        )

    def get_available_functions(self) -> Dict[str, Any]:
        """
        Get all available plugin functions.

        Returns:
            Dictionary of function metadata
        """
        functions = {}
        for name, metadata in self.loader.function_registry.get_all_functions().items():
            functions[name] = {
                'name': metadata.name,
                'plugin_name': metadata.plugin_name,
                'description': metadata.description,
                'parameters': [
                    {
                        'name': p.name,
                        'type': p.type.value,
                        'description': p.description,
                        'required': p.required,
                        'default': p.default
                    }
                    for p in metadata.parameters
                ],
                'return_type': metadata.return_type.value,
                'return_description': metadata.return_description,
                'examples': metadata.examples,
                'tags': metadata.tags,
                'deprecated': metadata.deprecated,
                'version': metadata.version
            }
        return functions

    def get_function_schema(self, function_name: str) -> Optional[Dict[str, Any]]:
        """
        Get OpenAPI-style schema for a function.

        Args:
            function_name: Name of the function

        Returns:
            Function schema or None if not found
        """
        return self.loader.function_registry.get_function_schema(function_name)

    def search_functions(self, query: str) -> List[Dict[str, Any]]:
        """
        Search for functions by name or description.

        Args:
            query: Search query

        Returns:
            List of matching function metadata
        """
        results = self.loader.function_registry.search_functions(query)
        return [
            {
                'name': metadata.name,
                'plugin_name': metadata.plugin_name,
                'description': metadata.description,
                'tags': metadata.tags
            }
            for metadata in results
        ]

    # Hot reload interface

    async def reload_plugin(self, plugin_name: str, force: bool = False):
        """
        Reload a specific plugin.

        Args:
            plugin_name: Name of the plugin to reload
            force: Force reload even if no changes detected

        Returns:
            Reload event with results
        """
        return await self.hot_reload_manager.reload_plugin(plugin_name, force)

    def get_reload_history(self, limit: Optional[int] = None):
        """
        Get plugin reload history.

        Args:
            limit: Maximum number of events to return

        Returns:
            List of reload events
        """
        return self.hot_reload_manager.get_reload_history(limit)

    def is_hot_reload_active(self) -> bool:
        """Check if hot reload system is active."""
        return self.hot_reload_manager.is_active