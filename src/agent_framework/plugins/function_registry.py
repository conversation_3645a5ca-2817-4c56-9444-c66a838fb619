"""
Plugin function registry for exposing plugin functions to the agent model.

Provides standardized function discovery, metadata extraction, and type-safe
calling mechanisms for AI model integration.
"""

import inspect
import logging
from typing import Any, Callable, Dict, List, Optional, Type, Union, get_type_hints
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

try:
    from pydantic import BaseModel, ValidationError, create_model
    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False
    BaseModel = None
    ValidationError = None


class ParameterType(Enum):
    """Types of function parameters."""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    ARRAY = "array"
    OBJECT = "object"
    ANY = "any"


@dataclass
class FunctionParameter:
    """Metadata for a function parameter."""
    name: str
    type: ParameterType
    description: str = ""
    required: bool = True
    default: Any = None
    schema: Optional[Dict[str, Any]] = None
    examples: List[Any] = field(default_factory=list)


@dataclass
class FunctionMetadata:
    """Metadata for an exposed plugin function."""
    name: str
    plugin_name: str
    description: str
    parameters: List[FunctionParameter]
    return_type: ParameterType = ParameterType.ANY
    return_description: str = ""
    return_schema: Optional[Dict[str, Any]] = None
    examples: List[Dict[str, Any]] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    deprecated: bool = False
    version: str = "1.0.0"
    
    # Function reference
    function: Optional[Callable] = None
    
    # Execution metadata
    registered_at: datetime = field(default_factory=datetime.now)
    call_count: int = 0
    last_called: Optional[datetime] = None
    average_execution_time: float = 0.0


@dataclass
class FunctionCallResult:
    """Result of a function call."""
    success: bool
    result: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    function_name: str = ""
    plugin_name: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)


class FunctionRegistry:
    """
    Registry for plugin functions exposed to the AI model.
    
    Provides function discovery, metadata management, and type-safe
    calling mechanisms for seamless AI model integration.
    """
    
    def __init__(self):
        """Initialize the function registry."""
        self.logger = logging.getLogger(__name__)
        self._functions: Dict[str, FunctionMetadata] = {}
        self._plugin_functions: Dict[str, List[str]] = {}  # plugin_name -> function_names
        self._function_aliases: Dict[str, str] = {}  # alias -> function_name
        self._categories: Dict[str, List[str]] = {}  # category -> function_names
    
    def register_function(self, 
                         function: Callable,
                         name: Optional[str] = None,
                         plugin_name: str = "",
                         description: str = "",
                         parameters: Optional[List[FunctionParameter]] = None,
                         return_type: ParameterType = ParameterType.ANY,
                         return_description: str = "",
                         examples: Optional[List[Dict[str, Any]]] = None,
                         tags: Optional[List[str]] = None,
                         category: Optional[str] = None,
                         aliases: Optional[List[str]] = None) -> str:
        """
        Register a function for AI model access.
        
        Args:
            function: The function to register
            name: Function name (defaults to function.__name__)
            plugin_name: Name of the plugin providing this function
            description: Function description
            parameters: Parameter metadata (auto-extracted if not provided)
            return_type: Return value type
            return_description: Description of return value
            examples: Usage examples
            tags: Function tags for categorization
            category: Function category
            aliases: Alternative names for the function
            
        Returns:
            The registered function name
        """
        # Determine function name
        func_name = name or function.__name__
        full_name = f"{plugin_name}.{func_name}" if plugin_name else func_name
        
        # Auto-extract parameters if not provided
        if parameters is None:
            parameters = self._extract_parameters(function)
        
        # Create metadata
        metadata = FunctionMetadata(
            name=full_name,
            plugin_name=plugin_name,
            description=description or function.__doc__ or "",
            parameters=parameters,
            return_type=return_type,
            return_description=return_description,
            examples=examples or [],
            tags=tags or [],
            function=function
        )
        
        # Register the function
        self._functions[full_name] = metadata
        
        # Update plugin function mapping
        if plugin_name:
            if plugin_name not in self._plugin_functions:
                self._plugin_functions[plugin_name] = []
            self._plugin_functions[plugin_name].append(full_name)
        
        # Register aliases
        if aliases:
            for alias in aliases:
                self._function_aliases[alias] = full_name
        
        # Register category
        if category:
            if category not in self._categories:
                self._categories[category] = []
            self._categories[category].append(full_name)
        
        self.logger.info(f"Registered function: {full_name}")
        return full_name
    
    def unregister_function(self, function_name: str) -> bool:
        """
        Unregister a function.
        
        Args:
            function_name: Name of the function to unregister
            
        Returns:
            True if function was unregistered, False if not found
        """
        if function_name not in self._functions:
            return False
        
        metadata = self._functions[function_name]
        
        # Remove from main registry
        del self._functions[function_name]
        
        # Remove from plugin mapping
        if metadata.plugin_name in self._plugin_functions:
            if function_name in self._plugin_functions[metadata.plugin_name]:
                self._plugin_functions[metadata.plugin_name].remove(function_name)
            
            # Clean up empty plugin entries
            if not self._plugin_functions[metadata.plugin_name]:
                del self._plugin_functions[metadata.plugin_name]
        
        # Remove aliases
        aliases_to_remove = [alias for alias, name in self._function_aliases.items() 
                           if name == function_name]
        for alias in aliases_to_remove:
            del self._function_aliases[alias]
        
        # Remove from categories
        for category, functions in self._categories.items():
            if function_name in functions:
                functions.remove(function_name)
        
        # Clean up empty categories
        empty_categories = [cat for cat, funcs in self._categories.items() if not funcs]
        for cat in empty_categories:
            del self._categories[cat]
        
        self.logger.info(f"Unregistered function: {function_name}")
        return True
    
    def unregister_plugin_functions(self, plugin_name: str) -> int:
        """
        Unregister all functions from a plugin.
        
        Args:
            plugin_name: Name of the plugin
            
        Returns:
            Number of functions unregistered
        """
        if plugin_name not in self._plugin_functions:
            return 0
        
        function_names = self._plugin_functions[plugin_name].copy()
        count = 0
        
        for function_name in function_names:
            if self.unregister_function(function_name):
                count += 1
        
        return count
    
    async def call_function(self, 
                          function_name: str, 
                          arguments: Optional[Dict[str, Any]] = None,
                          validate_args: bool = True) -> FunctionCallResult:
        """
        Call a registered function.
        
        Args:
            function_name: Name of the function to call
            arguments: Function arguments
            validate_args: Whether to validate arguments
            
        Returns:
            Function call result
        """
        import time
        
        start_time = time.time()
        
        # Resolve aliases
        actual_name = self._function_aliases.get(function_name, function_name)
        
        if actual_name not in self._functions:
            return FunctionCallResult(
                success=False,
                error=f"Function not found: {function_name}",
                function_name=function_name
            )
        
        metadata = self._functions[actual_name]
        
        try:
            # Validate arguments if requested
            if validate_args and arguments:
                validation_error = self._validate_arguments(metadata, arguments)
                if validation_error:
                    return FunctionCallResult(
                        success=False,
                        error=f"Argument validation failed: {validation_error}",
                        function_name=actual_name,
                        plugin_name=metadata.plugin_name
                    )
            
            # Prepare function call
            call_args = arguments or {}
            
            # Call the function
            if inspect.iscoroutinefunction(metadata.function):
                result = await metadata.function(**call_args)
            else:
                result = metadata.function(**call_args)
            
            execution_time = time.time() - start_time
            
            # Update call statistics
            metadata.call_count += 1
            metadata.last_called = datetime.now()
            metadata.average_execution_time = (
                (metadata.average_execution_time * (metadata.call_count - 1) + execution_time) 
                / metadata.call_count
            )
            
            return FunctionCallResult(
                success=True,
                result=result,
                execution_time=execution_time,
                function_name=actual_name,
                plugin_name=metadata.plugin_name
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            return FunctionCallResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                function_name=actual_name,
                plugin_name=metadata.plugin_name
            )
    
    def get_function(self, function_name: str) -> Optional[FunctionMetadata]:
        """Get function metadata by name."""
        actual_name = self._function_aliases.get(function_name, function_name)
        return self._functions.get(actual_name)
    
    def get_all_functions(self) -> Dict[str, FunctionMetadata]:
        """Get all registered functions."""
        return self._functions.copy()
    
    def get_plugin_functions(self, plugin_name: str) -> List[FunctionMetadata]:
        """Get all functions from a specific plugin."""
        if plugin_name not in self._plugin_functions:
            return []
        
        return [self._functions[name] for name in self._plugin_functions[plugin_name]]
    
    def get_functions_by_category(self, category: str) -> List[FunctionMetadata]:
        """Get functions by category."""
        if category not in self._categories:
            return []
        
        return [self._functions[name] for name in self._categories[category]]
    
    def get_functions_by_tags(self, tags: List[str]) -> List[FunctionMetadata]:
        """Get functions that have any of the specified tags."""
        matching_functions = []
        
        for metadata in self._functions.values():
            if any(tag in metadata.tags for tag in tags):
                matching_functions.append(metadata)
        
        return matching_functions
    
    def search_functions(self, query: str) -> List[FunctionMetadata]:
        """Search functions by name or description."""
        query_lower = query.lower()
        matching_functions = []
        
        for metadata in self._functions.values():
            if (query_lower in metadata.name.lower() or 
                query_lower in metadata.description.lower()):
                matching_functions.append(metadata)
        
        return matching_functions
    
    def get_function_schema(self, function_name: str) -> Optional[Dict[str, Any]]:
        """Get OpenAPI-style schema for a function."""
        metadata = self.get_function(function_name)
        if not metadata:
            return None
        
        properties = {}
        required = []
        
        for param in metadata.parameters:
            param_schema = {
                "type": param.type.value,
                "description": param.description
            }
            
            if param.schema:
                param_schema.update(param.schema)
            
            if param.examples:
                param_schema["examples"] = param.examples
            
            if param.default is not None:
                param_schema["default"] = param.default
            
            properties[param.name] = param_schema
            
            if param.required:
                required.append(param.name)
        
        return {
            "name": metadata.name,
            "description": metadata.description,
            "parameters": {
                "type": "object",
                "properties": properties,
                "required": required
            },
            "returns": {
                "type": metadata.return_type.value,
                "description": metadata.return_description
            }
        }
    
    def _extract_parameters(self, function: Callable) -> List[FunctionParameter]:
        """Extract parameter metadata from function signature."""
        parameters = []
        
        try:
            sig = inspect.signature(function)
            type_hints = get_type_hints(function)
            
            for param_name, param in sig.parameters.items():
                # Skip self parameter
                if param_name == 'self':
                    continue
                
                # Determine parameter type
                param_type = ParameterType.ANY
                if param_name in type_hints:
                    hint = type_hints[param_name]
                    param_type = self._type_hint_to_parameter_type(hint)
                
                # Check if required
                required = param.default == inspect.Parameter.empty
                default = None if required else param.default
                
                parameters.append(FunctionParameter(
                    name=param_name,
                    type=param_type,
                    required=required,
                    default=default
                ))
                
        except Exception as e:
            self.logger.warning(f"Failed to extract parameters for {function.__name__}: {e}")
        
        return parameters
    
    def _type_hint_to_parameter_type(self, hint: Type) -> ParameterType:
        """Convert Python type hint to ParameterType."""
        if hint == str:
            return ParameterType.STRING
        elif hint == int:
            return ParameterType.INTEGER
        elif hint == float:
            return ParameterType.FLOAT
        elif hint == bool:
            return ParameterType.BOOLEAN
        elif hasattr(hint, '__origin__'):
            if hint.__origin__ == list:
                return ParameterType.ARRAY
            elif hint.__origin__ == dict:
                return ParameterType.OBJECT
        
        return ParameterType.ANY
    
    def _validate_arguments(self, metadata: FunctionMetadata, arguments: Dict[str, Any]) -> Optional[str]:
        """Validate function arguments against parameter metadata."""
        # Check required parameters
        for param in metadata.parameters:
            if param.required and param.name not in arguments:
                return f"Missing required parameter: {param.name}"
        
        # Check parameter types (basic validation)
        for param_name, value in arguments.items():
            param = next((p for p in metadata.parameters if p.name == param_name), None)
            if param:
                error = self._validate_parameter_value(param, value)
                if error:
                    return f"Parameter {param_name}: {error}"
        
        return None
    
    def _validate_parameter_value(self, param: FunctionParameter, value: Any) -> Optional[str]:
        """Validate a single parameter value."""
        if param.type == ParameterType.STRING and not isinstance(value, str):
            return f"Expected string, got {type(value).__name__}"
        elif param.type == ParameterType.INTEGER and not isinstance(value, int):
            return f"Expected integer, got {type(value).__name__}"
        elif param.type == ParameterType.FLOAT and not isinstance(value, (int, float)):
            return f"Expected number, got {type(value).__name__}"
        elif param.type == ParameterType.BOOLEAN and not isinstance(value, bool):
            return f"Expected boolean, got {type(value).__name__}"
        elif param.type == ParameterType.ARRAY and not isinstance(value, list):
            return f"Expected array, got {type(value).__name__}"
        elif param.type == ParameterType.OBJECT and not isinstance(value, dict):
            return f"Expected object, got {type(value).__name__}"
        
        return None
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """Get registry statistics."""
        return {
            "total_functions": len(self._functions),
            "total_plugins": len(self._plugin_functions),
            "total_categories": len(self._categories),
            "total_aliases": len(self._function_aliases),
            "functions_by_plugin": {
                plugin: len(functions) 
                for plugin, functions in self._plugin_functions.items()
            },
            "functions_by_category": {
                category: len(functions)
                for category, functions in self._categories.items()
            }
        }
