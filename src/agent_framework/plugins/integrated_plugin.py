"""
Integrated plugin base class with shared infrastructure.

This module provides an enhanced plugin base that integrates with
the shared infrastructure for better component coupling.
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime

from ..core.types import PluginInterface, PluginRequest, PluginResponse, PluginCapability
from ..shared.base_processor import BaseProcessor, ProcessingContext
from ..shared.base_cache import BaseCache
from ..shared.data_models import StandardResult, CapabilityModel
from ..communication.enhanced_broker import EnhancedMessageBroker, TypedEvent
from ..utils.validation_utils import validate_json_schema


class IntegratedPlugin(PluginInterface, BaseProcessor[PluginRequest, PluginResponse]):
    """
    Enhanced plugin with integrated shared infrastructure.
    
    Combines the plugin interface with shared processing patterns,
    caching, and improved communication capabilities.
    """
    
    def __init__(self, plugin_name: str, version: str,
                 message_broker: Optional[EnhancedMessageBroker] = None,
                 cache: Optional[BaseCache] = None):
        """
        Initialize the integrated plugin.
        
        Args:
            plugin_name: Plugin name
            version: Plugin version
            message_broker: Enhanced message broker for communication
            cache: Cache for performance optimization
        """
        # Initialize base classes
        PluginInterface.__init__(self)

        self._name = plugin_name
        # Initialize BaseProcessor manually to avoid name conflict
        self.config = {}
        self.logger = logging.getLogger(f"{__name__}.{plugin_name}")
        from ..shared.base_metrics import BaseMetrics
        self.metrics = BaseMetrics(f"{plugin_name}_processor")
        self._is_initialized = False
        self._processing_count = 0
        self._total_processed = 0
        self._total_errors = 0
        self._version = version
        
        # Shared infrastructure
        self.message_broker = message_broker
        self.cache = cache
        
        # Plugin state
        self._capabilities: List[PluginCapability] = []
        self._config: Dict[str, Any] = {}
        self._is_initialized = False
        
        # Performance tracking
        self._requests_processed = 0
        self._requests_failed = 0
        self._average_execution_time = 0.0
    
    @property
    def name(self) -> str:
        """Get the plugin name."""
        return self._name
    
    @property
    def version(self) -> str:
        """Get the plugin version."""
        return self._version
    
    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the integrated plugin."""
        self._config = config
        
        # Initialize base processor
        await BaseProcessor.initialize(self)
        
        # Initialize plugin-specific components
        await self._initialize_plugin()
        
        # Set up event subscriptions if message broker is available
        if self.message_broker:
            await self._setup_event_subscriptions()
        
        self._is_initialized = True
        self.logger.info(f"Integrated plugin {self.name} initialized")
    
    async def _initialize_plugin(self) -> None:
        """Plugin-specific initialization. Override in subclasses."""
        pass
    
    async def _setup_event_subscriptions(self) -> None:
        """Set up event subscriptions for plugin communication."""
        if not self.message_broker:
            return
        
        # Subscribe to plugin requests
        self.message_broker.subscribe(
            event_type="plugin_request",
            handler=self._handle_plugin_request,
            subscriber_id=f"plugin_{self.name}",
            filter_func=lambda event: event.payload.get("plugin_name") == self.name
        )
        
        # Subscribe to capability queries
        self.message_broker.subscribe(
            event_type="plugin_capability_query",
            handler=self._handle_capability_query,
            subscriber_id=f"plugin_{self.name}"
        )
    
    async def _validate_input(self, request: PluginRequest, context: ProcessingContext) -> bool:
        """Validate plugin request input."""
        # Check if plugin supports the requested capability
        if not any(cap.name == request.capability for cap in self._capabilities):
            return False
        
        # Validate request parameters against capability schema
        capability = next(
            (cap for cap in self._capabilities if cap.name == request.capability),
            None
        )
        
        if capability and capability.input_schema:
            validation_result = validate_json_schema(
                request.parameters,
                capability.input_schema
            )
            if not validation_result["is_valid"]:
                self.logger.error(f"Request validation failed: {validation_result['errors']}")
                return False
        
        return True
    
    async def _process_data(self, request: PluginRequest, context: ProcessingContext) -> Any:
        """Process the plugin request using integrated infrastructure."""
        try:
            # Check cache for previous results
            cache_key = self._generate_cache_key(request)
            if self.cache:
                cached_result = await self.cache.get(cache_key)
                if cached_result:
                    self.logger.debug(f"Using cached result for request {request.capability}")
                    return cached_result
            
            # Execute the plugin capability
            result = await self._execute_capability(request, context)

            # Cache the result
            if self.cache:
                await self.cache.set(cache_key, result, ttl=1800)  # 30 minutes TTL

            # Update metrics
            self._requests_processed += 1

            # Store metadata for later use
            context.metadata.update({
                "plugin_name": self.name,
                "plugin_version": self.version,
                "capability": request.capability,
                "timestamp": datetime.now().isoformat()
            })

            return result
            
        except Exception as e:
            self._requests_failed += 1
            
            error_response = PluginResponse(
                success=False,
                error=str(e),
                metadata={
                    "plugin_name": self.name,
                    "plugin_version": self.version,
                    "capability": request.capability,
                    "timestamp": datetime.now().isoformat()
                }
            )
            
            # Publish failure event
            if self.message_broker:
                await self._publish_request_failure(request, str(e))
            
            return error_response
    
    async def _handle_error(self, error: Exception, request: PluginRequest, 
                          context: ProcessingContext) -> Optional[PluginResponse]:
        """Handle processing errors with recovery attempts."""
        self.logger.error(f"Error processing request {request.capability}: {error}")
        
        # Try to provide a meaningful error response
        return PluginResponse(
            success=False,
            error=str(error),
            metadata={
                "plugin_name": self.name,
                "error_type": type(error).__name__,
                "recovery_attempted": True
            }
        )
    
    async def execute(self, request: PluginRequest) -> PluginResponse:
        """Execute a plugin request."""
        if not self._is_initialized:
            return PluginResponse(
                success=False,
                error="Plugin not initialized"
            )
        
        # Create processing context
        context = ProcessingContext(
            task_id=f"{self.name}_{request.capability}_{datetime.now().timestamp()}",
            user_id=getattr(request, 'user_id', None),
            session_id=getattr(request, 'session_id', None)
        )
        
        # Use base processor to handle the request
        result = await self.process(request, context)
        
        # Convert StandardResult to PluginResponse
        if isinstance(result, StandardResult):
            # Merge context metadata with result metadata
            merged_metadata = {
                "plugin_name": self.name,
                "plugin_version": self.version,
                "capability": request.capability,
                "timestamp": datetime.now().isoformat()
            }
            if result.metadata:
                merged_metadata.update(result.metadata)

            response = PluginResponse(
                success=result.success,
                result=result.result,
                error=result.error,
                execution_time=result.execution_time or 0.0,
                metadata=merged_metadata
            )

            # Publish completion event
            if self.message_broker:
                await self._publish_request_completion(request, response)

            return response

        # Fallback for direct PluginResponse
        return result
    
    async def _execute_capability(self, request: PluginRequest, context: ProcessingContext) -> Any:
        """
        Execute a specific capability. Override in subclasses.
        
        Args:
            request: Plugin request
            context: Processing context
            
        Returns:
            Capability execution result
        """
        raise NotImplementedError("Subclasses must implement _execute_capability")
    
    async def get_capabilities(self) -> List[PluginCapability]:
        """Get the capabilities provided by this plugin."""
        return self._capabilities
    
    def add_capability(self, capability: PluginCapability) -> None:
        """Add a capability to the plugin."""
        self._capabilities.append(capability)
    
    async def cleanup(self) -> None:
        """Clean up plugin resources."""
        # Unsubscribe from events
        if self.message_broker:
            self.message_broker.unsubscribe("plugin_request", f"plugin_{self.name}")
            self.message_broker.unsubscribe("plugin_capability_query", f"plugin_{self.name}")
        
        # Shutdown base processor
        await BaseProcessor.shutdown(self)
        
        self._is_initialized = False
        self.logger.info(f"Plugin {self.name} cleaned up")
    
    def _generate_cache_key(self, request: PluginRequest) -> str:
        """Generate cache key for request."""
        if self.cache:
            return self.cache.generate_key(
                self.name,
                request.capability,
                request.parameters
            )
        return ""
    
    async def _handle_plugin_request(self, event: TypedEvent) -> None:
        """Handle plugin request event."""
        request_data = event.payload
        request = PluginRequest(**request_data)
        
        try:
            response = await self.execute(request)
            
            # Send response if this was a request
            if event.correlation_id and self.message_broker:
                await self.message_broker.send_response(
                    event.correlation_id,
                    StandardResult.success_result(response.__dict__)
                )
        except Exception as e:
            if event.correlation_id and self.message_broker:
                await self.message_broker.send_response(
                    event.correlation_id,
                    StandardResult.error_result(str(e))
                )
    
    async def _handle_capability_query(self, event: TypedEvent) -> None:
        """Handle capability query event."""
        # Respond with plugin capabilities
        if self.message_broker:
            capabilities_data = []
            for cap in self._capabilities:
                capabilities_data.append({
                    "name": cap.name,
                    "description": cap.description,
                    "input_schema": cap.input_schema,
                    "output_schema": cap.output_schema,
                    "supported_languages": cap.supported_languages
                })
            
            response_event = TypedEvent(
                event_type="plugin_capability_response",
                payload={
                    "plugin_name": self.name,
                    "plugin_version": self.version,
                    "capabilities": capabilities_data,
                    "performance_metrics": {
                        "requests_processed": self._requests_processed,
                        "requests_failed": self._requests_failed,
                        "average_execution_time": self._average_execution_time
                    }
                },
                source=f"plugin_{self.name}",
                correlation_id=event.correlation_id
            )
            await self.message_broker.publish_event(response_event)
    
    async def _publish_request_completion(self, request: PluginRequest, response: PluginResponse) -> None:
        """Publish request completion event."""
        if self.message_broker:
            event = TypedEvent(
                event_type="plugin_request_completed",
                payload={
                    "plugin_name": self.name,
                    "capability": request.capability,
                    "success": response.success,
                    "execution_time": response.execution_time
                },
                source=f"plugin_{self.name}"
            )
            await self.message_broker.publish_event(event)
    
    async def _publish_request_failure(self, request: PluginRequest, error: str) -> None:
        """Publish request failure event."""
        if self.message_broker:
            event = TypedEvent(
                event_type="plugin_request_failed",
                payload={
                    "plugin_name": self.name,
                    "capability": request.capability,
                    "error": error
                },
                source=f"plugin_{self.name}"
            )
            await self.message_broker.publish_event(event)
    
    def get_plugin_stats(self) -> Dict[str, Any]:
        """Get plugin statistics."""
        return {
            "name": self.name,
            "version": self.version,
            "requests_processed": self._requests_processed,
            "requests_failed": self._requests_failed,
            "average_execution_time": self._average_execution_time,
            "capabilities_count": len(self._capabilities),
            "is_initialized": self._is_initialized
        }
    
    async def collaborate_with_plugin(self, other_plugin_name: str, 
                                    capability: str, parameters: Dict[str, Any]) -> Any:
        """
        Collaborate with another plugin.
        
        Args:
            other_plugin_name: Name of the plugin to collaborate with
            capability: Capability to request
            parameters: Parameters for the capability
            
        Returns:
            Result from the other plugin
        """
        if not self.message_broker:
            raise RuntimeError("Message broker not available for plugin collaboration")
        
        # Create collaboration request
        request = PluginRequest(
            capability=capability,
            parameters=parameters
        )
        
        # Send request via message broker
        collaboration_event = TypedEvent(
            event_type="plugin_request",
            payload={
                "plugin_name": other_plugin_name,
                **request.__dict__
            },
            source=f"plugin_{self.name}",
            target=f"plugin_{other_plugin_name}"
        )
        
        # This would need to be implemented as a request-response pattern
        await self.message_broker.publish_event(collaboration_event)
        
        # For now, return a placeholder
        return {"collaboration_result": f"Requested {capability} from {other_plugin_name}"}
