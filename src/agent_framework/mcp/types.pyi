"""
Types and data structures for MCP integration.
"""

import asyncio
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from pydantic import BaseModel

class MCPConnectionStatus(Enum):
    """Status of an MCP server connection."""
    DISCONNECTED: str
    CONNECTING: str
    CONNECTED: str
    ERROR: str
    RECONNECTING: str

class MCPCapabilityType(Enum):
    """Types of MCP capabilities."""
    TOOLS: str
    RESOURCES: str
    PROMPTS: str
    SAMPLING: str
    ROOTS: str
    ELICITATION: str

@dataclass
class MCPServerInfo:
    """Information about an MCP server."""
    name: str
    command: List[str]
    args: List[str]
    env: Dict[str, str]
    transport_type: str
    timeout_seconds: int
    max_retries: int
    enabled: bool
    description: str
    version: str
    connection_attempts: int
    last_error: Optional[str]
    last_connected: Optional[datetime]
    supported_capabilities: List[MCPCapabilityType]
    tools: List[Dict[str, Any]]
    resources: List[Dict[str, Any]]
    prompts: List[Dict[str, Any]]

class MCPError(Exception):
    """Base exception for MCP-related errors."""
    
    def __init__(self, message: str, server_name: Optional[str] = None, error_code: Optional[str] = None) -> None: ...
    
    @property
    def server_name(self) -> Optional[str]: ...
    
    @property
    def error_code(self) -> Optional[str]: ...
    
    @property
    def timestamp(self) -> datetime: ...

class MCPConnectionError(MCPError):
    """Exception raised when MCP connection fails."""
    pass

class MCPTimeoutError(MCPError):
    """Exception raised when MCP operation times out."""
    pass

class MCPToolCallRequest(BaseModel):
    """Request to call an MCP tool."""
    tool_name: str
    arguments: Dict[str, Any]
    timeout: Optional[float]

class MCPToolCallResponse(BaseModel):
    """Response from MCP tool call."""
    success: bool
    result: Optional[Any]
    error: Optional[str]
    execution_time: float

class MCPResourceRequest(BaseModel):
    """Request for MCP resource."""
    resource_uri: str
    parameters: Optional[Dict[str, Any]]

class MCPResourceResponse(BaseModel):
    """Response from MCP resource request."""
    content: str
    mime_type: str
    metadata: Dict[str, Any]

class MCPPromptRequest(BaseModel):
    """Request for MCP prompt."""
    prompt_name: str
    arguments: Dict[str, Any]

class MCPPromptResponse(BaseModel):
    """Response from MCP prompt request."""
    messages: List[Dict[str, Any]]
    metadata: Dict[str, Any]

class MCPServerCapabilities(BaseModel):
    """Capabilities of an MCP server."""
    tools: Optional[Dict[str, Any]]
    resources: Optional[Dict[str, Any]]
    prompts: Optional[Dict[str, Any]]
    sampling: Optional[Dict[str, Any]]
    roots: Optional[Dict[str, Any]]
    elicitation: Optional[Dict[str, Any]]

class MCPInitializeRequest(BaseModel):
    """MCP initialize request."""
    protocol_version: str
    capabilities: Dict[str, Any]
    client_info: Dict[str, Any]

class MCPConnectionMetrics(BaseModel):
    """Metrics for MCP connection."""
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_response_time: float
    last_request_time: Optional[datetime]
    connection_uptime: float
