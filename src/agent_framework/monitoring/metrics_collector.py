"""
Metrics collection system for the agent framework.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, deque
from enum import Enum

from ..core.config import FrameworkConfig


class MetricType(Enum):
    """Types of metrics that can be collected."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


@dataclass
class Metric:
    """A single metric data point."""
    name: str
    value: float
    metric_type: MetricType
    timestamp: datetime
    tags: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class MetricSummary:
    """Summary statistics for a metric."""
    name: str
    count: int
    min_value: float
    max_value: float
    avg_value: float
    sum_value: float
    last_value: float
    last_updated: datetime


class MetricsCollector:
    """
    Collects and aggregates metrics from various components of the agent framework.
    """
    
    def __init__(self, config: FrameworkConfig):
        """Initialize the metrics collector."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Metrics storage
        self._metrics: Dict[str, deque[Metric]] = defaultdict(lambda: deque(maxlen=10000))
        self._counters: Dict[str, float] = defaultdict(float)
        self._gauges: Dict[str, float] = defaultdict(float)
        self._timers: Dict[str, List[float]] = defaultdict(list)
        
        # Collection state
        self._is_collecting = False
        self._collection_callbacks: List[Callable] = []
        
        # Aggregation settings
        self._retention_hours = 24
        self._cleanup_interval = 3600  # 1 hour
        self._last_cleanup = datetime.now()
    
    def start_collection(self):
        """Start metrics collection."""
        self._is_collecting = True
        self.logger.info("Metrics collection started")
    
    def stop_collection(self):
        """Stop metrics collection."""
        self._is_collecting = False
        self.logger.info("Metrics collection stopped")
    
    def record_counter(self, name: str, value: float = 1.0, tags: Optional[Dict[str, str]] = None):
        """Record a counter metric (cumulative value)."""
        if not self._is_collecting:
            return
        
        self._counters[name] += value
        
        metric = Metric(
            name=name,
            value=value,
            metric_type=MetricType.COUNTER,
            timestamp=datetime.now(),
            tags=tags or {}
        )
        
        self._metrics[name].append(metric)
        self._maybe_cleanup()
    
    def record_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """Record a gauge metric (point-in-time value)."""
        if not self._is_collecting:
            return
        
        self._gauges[name] = value
        
        metric = Metric(
            name=name,
            value=value,
            metric_type=MetricType.GAUGE,
            timestamp=datetime.now(),
            tags=tags or {}
        )
        
        self._metrics[name].append(metric)
        self._maybe_cleanup()
    
    def record_histogram(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """Record a histogram metric (distribution of values)."""
        if not self._is_collecting:
            return
        
        metric = Metric(
            name=name,
            value=value,
            metric_type=MetricType.HISTOGRAM,
            timestamp=datetime.now(),
            tags=tags or {}
        )
        
        self._metrics[name].append(metric)
        self._maybe_cleanup()
    
    def start_timer(self, name: str) -> 'TimerContext':
        """Start a timer context for measuring execution time."""
        return TimerContext(self, name)
    
    def record_timer(self, name: str, duration: float, tags: Optional[Dict[str, str]] = None):
        """Record a timer metric (execution duration)."""
        if not self._is_collecting:
            return
        
        self._timers[name].append(duration)
        
        metric = Metric(
            name=name,
            value=duration,
            metric_type=MetricType.TIMER,
            timestamp=datetime.now(),
            tags=tags or {}
        )
        
        self._metrics[name].append(metric)
        self._maybe_cleanup()
    
    def get_metric_summary(self, name: str, hours: int = 1) -> Optional[MetricSummary]:
        """Get summary statistics for a metric over the specified time period."""
        if name not in self._metrics:
            return None
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_metrics = [
            m for m in self._metrics[name] 
            if m.timestamp >= cutoff_time
        ]
        
        if not recent_metrics:
            return None
        
        values = [m.value for m in recent_metrics]
        
        return MetricSummary(
            name=name,
            count=len(values),
            min_value=min(values),
            max_value=max(values),
            avg_value=sum(values) / len(values),
            sum_value=sum(values),
            last_value=values[-1],
            last_updated=recent_metrics[-1].timestamp
        )
    
    def get_all_metrics(self, hours: int = 1) -> Dict[str, List[Metric]]:
        """Get all metrics for the specified time period."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        result: Dict[str, List[Metric]] = {}
        
        for name, metrics in self._metrics.items():
            recent_metrics = [
                m for m in metrics 
                if m.timestamp >= cutoff_time
            ]
            if recent_metrics:
                result[name] = recent_metrics
        
        return result
    
    def get_counter_value(self, name: str) -> float:
        """Get current value of a counter."""
        return self._counters.get(name, 0.0)
    
    def get_gauge_value(self, name: str) -> Optional[float]:
        """Get current value of a gauge."""
        return self._gauges.get(name)
    
    def get_timer_stats(self, name: str, hours: int = 1) -> Optional[Dict[str, float]]:
        """Get statistics for a timer metric."""
        if name not in self._timers:
            return None
        
        # Get recent timer values
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_values: List[float] = []
        
        if name in self._metrics:
            recent_metrics = [
                m for m in self._metrics[name] 
                if m.timestamp >= cutoff_time and m.metric_type == MetricType.TIMER
            ]
            recent_values = [m.value for m in recent_metrics]
        
        if not recent_values:
            return None
        
        recent_values.sort()
        count = len(recent_values)
        
        return {
            'count': count,
            'min': min(recent_values),
            'max': max(recent_values),
            'avg': sum(recent_values) / count,
            'median': recent_values[count // 2],
            'p95': recent_values[int(count * 0.95)] if count > 0 else 0.0,
            'p99': recent_values[int(count * 0.99)] if count > 0 else 0.0
        }
    
    def get_metrics_report(self, hours: int = 1) -> Dict[str, Any]:
        """Get a comprehensive metrics report."""
        report: Dict[str, Any] = {
            'period_hours': hours,
            'generated_at': datetime.now().isoformat(),
            'counters': {},   # type: Dict[str, Any]
            'gauges': {},     # type: Dict[str, Any]
            'histograms': {}, # type: Dict[str, Any]
            'timers': {}      # type: Dict[str, Any]
        }
        
        # Get all metric names
        all_names = set(self._metrics.keys())
        
        for name in all_names:
            summary = self.get_metric_summary(name, hours)
            if not summary:
                continue
            
            # Determine metric type from recent metrics
            recent_metrics = [
                m for m in self._metrics[name] 
                if m.timestamp >= datetime.now() - timedelta(hours=hours)
            ]
            
            if not recent_metrics:
                continue
            
            metric_type = recent_metrics[-1].metric_type
            
            summary_dict = {
                'count': summary.count,
                'min': summary.min_value,
                'max': summary.max_value,
                'avg': summary.avg_value,
                'sum': summary.sum_value,
                'last': summary.last_value,
                'last_updated': summary.last_updated.isoformat()
            }
            
            if metric_type == MetricType.COUNTER:
                report['counters'][name] = summary_dict
            elif metric_type == MetricType.GAUGE:
                report['gauges'][name] = summary_dict
            elif metric_type == MetricType.HISTOGRAM:
                report['histograms'][name] = summary_dict
            elif metric_type == MetricType.TIMER:
                timer_stats = self.get_timer_stats(name, hours)
                if timer_stats:
                    report['timers'][name] = timer_stats
        
        return report
    
    def add_collection_callback(self, callback: Callable[[str, Metric], None]):
        """Add a callback that will be called for each metric collected."""
        self._collection_callbacks.append(callback)
    
    def reset_metrics(self):
        """Reset all collected metrics."""
        self._metrics.clear()
        self._counters.clear()
        self._gauges.clear()
        self._timers.clear()
        self.logger.info("All metrics reset")
    
    def _maybe_cleanup(self):
        """Clean up old metrics if needed."""
        now = datetime.now()
        if (now - self._last_cleanup).total_seconds() >= self._cleanup_interval:
            self._cleanup_old_metrics()
            self._last_cleanup = now
    
    def _cleanup_old_metrics(self):
        """Remove metrics older than retention period."""
        cutoff_time = datetime.now() - timedelta(hours=self._retention_hours)
        
        for name, metrics in self._metrics.items():
            # Convert to list to avoid modifying deque during iteration
            old_metrics: List[Metric] = []
            for metric in list(metrics):
                if metric.timestamp < cutoff_time:
                    old_metrics.append(metric)
            
            # Remove old metrics
            for old_metric in old_metrics:
                try:
                    metrics.remove(old_metric)
                except ValueError:
                    pass  # Metric already removed
        
        self.logger.debug(f"Cleaned up metrics older than {self._retention_hours} hours")


class TimerContext:
    """Context manager for timing operations."""
    
    def __init__(self, collector: MetricsCollector, name: str, tags: Optional[Dict[str, str]] = None):
        self.collector = collector
        self.name = name
        self.tags = tags or {}
        self.start_time: Optional[float] = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time is not None:
            duration = time.time() - self.start_time
            self.collector.record_timer(self.name, duration, self.tags)
