"""
Base cache class providing unified caching interface.
"""

import asyncio
import time
import json
import hashlib
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum


class CachePolicy(Enum):
    """Cache eviction policies."""
    LRU = "lru"  # Least Recently Used
    LFU = "lfu"  # Least Frequently Used
    TTL = "ttl"  # Time To Live
    FIFO = "fifo"  # First In, First Out


@dataclass
class CacheEntry:
    """Represents a cache entry."""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int
    ttl_seconds: Optional[int] = None
    
    @property
    def is_expired(self) -> bool:
        """Check if the entry has expired."""
        if self.ttl_seconds is None:
            return False
        
        age = (datetime.now() - self.created_at).total_seconds()
        return age > self.ttl_seconds
    
    @property
    def age_seconds(self) -> float:
        """Get the age of the entry in seconds."""
        return (datetime.now() - self.created_at).total_seconds()


@dataclass
class CacheStats:
    """Cache statistics."""
    total_entries: int
    total_size_bytes: int
    hit_count: int
    miss_count: int
    eviction_count: int
    
    @property
    def hit_rate(self) -> float:
        """Calculate cache hit rate."""
        total_requests = self.hit_count + self.miss_count
        return self.hit_count / total_requests if total_requests > 0 else 0.0
    
    @property
    def miss_rate(self) -> float:
        """Calculate cache miss rate."""
        return 1.0 - self.hit_rate


class BaseCache(ABC):
    """
    Base cache interface providing unified caching patterns.
    
    Supports multiple eviction policies, TTL, and async operations.
    """
    
    def __init__(self, name: str, max_size: int = 1000, 
                 default_ttl: Optional[int] = None,
                 policy: CachePolicy = CachePolicy.LRU):
        """
        Initialize the cache.
        
        Args:
            name: Cache name for identification
            max_size: Maximum number of entries
            default_ttl: Default TTL in seconds
            policy: Cache eviction policy
        """
        self.name = name
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.policy = policy
        
        # Cache storage
        self._entries: Dict[str, CacheEntry] = {}
        self._access_order: List[str] = []  # For LRU
        
        # Statistics
        self._hit_count = 0
        self._miss_count = 0
        self._eviction_count = 0
        
        # Cleanup task
        self._cleanup_task: Optional[asyncio.Task] = None
        self._cleanup_interval = 60  # seconds
    
    async def initialize(self) -> None:
        """Initialize the cache."""
        # Start cleanup task for TTL entries
        if self.default_ttl is not None or self.policy == CachePolicy.TTL:
            self._cleanup_task = asyncio.create_task(self._cleanup_expired_entries())
    
    async def shutdown(self) -> None:
        """Shutdown the cache."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
    
    async def get(self, key: str, default: Any = None) -> Any:
        """
        Get value from cache.
        
        Args:
            key: Cache key
            default: Default value if key not found
            
        Returns:
            Cached value or default
        """
        entry = self._entries.get(key)
        
        if entry is None or entry.is_expired:
            self._miss_count += 1
            if entry and entry.is_expired:
                await self.delete(key)
            return default
        
        # Update access statistics
        entry.last_accessed = datetime.now()
        entry.access_count += 1
        self._hit_count += 1
        
        # Update access order for LRU
        if self.policy == CachePolicy.LRU:
            self._access_order.remove(key)
            self._access_order.append(key)
        
        return entry.value
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """
        Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds (overrides default)
        """
        # Use provided TTL or default
        effective_ttl = ttl if ttl is not None else self.default_ttl
        
        # Create cache entry
        entry = CacheEntry(
            key=key,
            value=value,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            access_count=1,
            ttl_seconds=effective_ttl
        )
        
        # Check if we need to evict entries
        if len(self._entries) >= self.max_size and key not in self._entries:
            await self._evict_entry()
        
        # Store entry
        self._entries[key] = entry
        
        # Update access order for LRU
        if self.policy == CachePolicy.LRU:
            if key in self._access_order:
                self._access_order.remove(key)
            self._access_order.append(key)
    
    async def delete(self, key: str) -> bool:
        """
        Delete entry from cache.
        
        Args:
            key: Cache key to delete
            
        Returns:
            True if key was deleted, False if not found
        """
        if key in self._entries:
            del self._entries[key]
            
            if self.policy == CachePolicy.LRU and key in self._access_order:
                self._access_order.remove(key)
            
            return True
        
        return False
    
    async def clear(self) -> None:
        """Clear all cache entries."""
        self._entries.clear()
        self._access_order.clear()
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache and is not expired."""
        entry = self._entries.get(key)
        if entry is None:
            return False
        
        if entry.is_expired:
            await self.delete(key)
            return False
        
        return True
    
    async def get_or_set(self, key: str, factory: Callable[[], Any], 
                        ttl: Optional[int] = None) -> Any:
        """
        Get value from cache or set it using factory function.
        
        Args:
            key: Cache key
            factory: Function to generate value if not in cache
            ttl: Time to live in seconds
            
        Returns:
            Cached or generated value
        """
        value = await self.get(key)
        if value is not None:
            return value
        
        # Generate value
        if asyncio.iscoroutinefunction(factory):
            value = await factory()
        else:
            value = factory()
        
        await self.set(key, value, ttl)
        return value
    
    async def get_many(self, keys: List[str]) -> Dict[str, Any]:
        """
        Get multiple values from cache.
        
        Args:
            keys: List of cache keys
            
        Returns:
            Dictionary of key-value pairs for found entries
        """
        result = {}
        for key in keys:
            value = await self.get(key)
            if value is not None:
                result[key] = value
        return result
    
    async def set_many(self, items: Dict[str, Any], ttl: Optional[int] = None) -> None:
        """
        Set multiple values in cache.
        
        Args:
            items: Dictionary of key-value pairs to cache
            ttl: Time to live in seconds
        """
        for key, value in items.items():
            await self.set(key, value, ttl)
    
    async def delete_many(self, keys: List[str]) -> int:
        """
        Delete multiple entries from cache.
        
        Args:
            keys: List of cache keys to delete
            
        Returns:
            Number of entries actually deleted
        """
        deleted_count = 0
        for key in keys:
            if await self.delete(key):
                deleted_count += 1
        return deleted_count
    
    def get_stats(self) -> CacheStats:
        """Get cache statistics."""
        total_size = sum(
            len(str(entry.value).encode('utf-8'))
            for entry in self._entries.values()
        )
        
        return CacheStats(
            total_entries=len(self._entries),
            total_size_bytes=total_size,
            hit_count=self._hit_count,
            miss_count=self._miss_count,
            eviction_count=self._eviction_count
        )
    
    def reset_stats(self) -> None:
        """Reset cache statistics."""
        self._hit_count = 0
        self._miss_count = 0
        self._eviction_count = 0
    
    async def _evict_entry(self) -> None:
        """Evict an entry based on the cache policy."""
        if not self._entries:
            return
        
        key_to_evict = None
        
        if self.policy == CachePolicy.LRU:
            # Evict least recently used
            key_to_evict = self._access_order[0] if self._access_order else None
        
        elif self.policy == CachePolicy.LFU:
            # Evict least frequently used
            min_access_count = min(entry.access_count for entry in self._entries.values())
            for key, entry in self._entries.items():
                if entry.access_count == min_access_count:
                    key_to_evict = key
                    break
        
        elif self.policy == CachePolicy.FIFO:
            # Evict oldest entry
            oldest_time = min(entry.created_at for entry in self._entries.values())
            for key, entry in self._entries.items():
                if entry.created_at == oldest_time:
                    key_to_evict = key
                    break
        
        elif self.policy == CachePolicy.TTL:
            # Evict expired entries first, then oldest
            expired_keys = [
                key for key, entry in self._entries.items()
                if entry.is_expired
            ]
            if expired_keys:
                key_to_evict = expired_keys[0]
            else:
                # Fall back to FIFO
                oldest_time = min(entry.created_at for entry in self._entries.values())
                for key, entry in self._entries.items():
                    if entry.created_at == oldest_time:
                        key_to_evict = key
                        break
        
        if key_to_evict:
            await self.delete(key_to_evict)
            self._eviction_count += 1
    
    async def _cleanup_expired_entries(self) -> None:
        """Background task to clean up expired entries."""
        while True:
            try:
                await asyncio.sleep(self._cleanup_interval)
                
                expired_keys = [
                    key for key, entry in self._entries.items()
                    if entry.is_expired
                ]
                
                for key in expired_keys:
                    await self.delete(key)
                
            except asyncio.CancelledError:
                break
            except Exception:
                # Continue cleanup on errors
                pass
    
    def generate_key(self, *args, **kwargs) -> str:
        """
        Generate a cache key from arguments.
        
        Args:
            *args: Positional arguments
            **kwargs: Keyword arguments
            
        Returns:
            Generated cache key
        """
        # Create a deterministic key from arguments
        key_data = {
            'args': args,
            'kwargs': sorted(kwargs.items())
        }
        
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
