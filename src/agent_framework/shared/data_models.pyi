"""
Unified data models for the agent framework.
"""

import uuid
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Generic, TypeVar

T = TypeVar('T')

class TaskStatus(Enum):
    """Status of a task."""
    PENDING: str
    RUNNING: str
    COMPLETED: str
    FAILED: str
    CANCELLED: str

class TaskPriority(Enum):
    """Priority levels for tasks."""
    LOW: int
    NORMAL: int
    HIGH: int
    CRITICAL: int

@dataclass
class UnifiedTask:
    """Unified task model used across all components."""
    id: str
    name: str
    description: str
    task_type: str
    priority: TaskPriority
    status: TaskStatus
    parameters: Dict[str, Any]
    context: Dict[str, Any]
    requirements: List[str]
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    timeout_seconds: Optional[int]
    retry_count: int
    max_retries: int
    dependencies: List[str]
    tags: List[str]
    metadata: Dict[str, Any]

@dataclass
class StandardResult(Generic[T]):
    """Standardized result format used across all components."""
    success: bool
    result: Optional[T]
    error: Optional[str]
    error_code: Optional[str]
    error_details: Optional[Dict[str, Any]]
    execution_time: Optional[float]
    timestamp: datetime
    metadata: Dict[str, Any]
    warnings: List[str]
    trace_id: Optional[str]
    span_id: Optional[str]

@dataclass
class CapabilityModel:
    """Model for describing component capabilities."""
    name: str
    description: str
    version: str
    category: str
    input_schema: Dict[str, Any]
    output_schema: Dict[str, Any]
    examples: List[Dict[str, Any]]
    requirements: List[str]
    limitations: List[str]
    performance_metrics: Dict[str, Any]
    supported_formats: List[str]
    configuration_options: Dict[str, Any]

@dataclass
class ContextModel:
    """Model for context information."""
    session_id: str
    user_id: Optional[str]
    workspace_path: Optional[str]
    active_files: List[str]
    recent_operations: List[Dict[str, Any]]
    preferences: Dict[str, Any]
    environment_info: Dict[str, Any]
    custom_context: Dict[str, Any]
    created_at: datetime
    updated_at: datetime

@dataclass
class MetricsSnapshot:
    """Snapshot of system metrics."""
    timestamp: datetime
    component_name: str
    metrics: Dict[str, Any]
    performance_data: Dict[str, float]
    resource_usage: Dict[str, Any]
    error_counts: Dict[str, int]
    success_rates: Dict[str, float]
    throughput_metrics: Dict[str, float]
    latency_metrics: Dict[str, float]

@dataclass
class UnifiedRequest:
    """Unified request model for component communication."""
    id: str
    request_type: str
    target: str
    payload: Dict[str, Any]
    parameters: Dict[str, Any]
    context: Optional[ContextModel]
    task_id: Optional[str]
    session_id: Optional[str]
    user_id: Optional[str]
    timeout_seconds: Optional[int]
    priority: TaskPriority
    async_execution: bool
    callback_url: Optional[str]
    correlation_id: Optional[str]
    created_at: datetime
    metadata: Dict[str, Any]
