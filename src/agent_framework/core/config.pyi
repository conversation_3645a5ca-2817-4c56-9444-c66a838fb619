"""
Configuration management for the agent framework.
"""

from typing import Any, Dict, List, Optional, Union
from enum import Enum
from pydantic import BaseModel, Field

class ModelProvider(Enum):
    """Supported AI model providers."""
    OPENAI: str
    ANTHROPIC: str
    OPENROUTER: str
    AZURE: str
    LOCAL: str

class ModelConfig(BaseModel):
    """Configuration for the AI model client."""
    provider: ModelProvider
    model: str
    api_key: str
    base_url: str
    model_info: Dict[str, Any]
    temperature: float
    max_tokens: int
    timeout_seconds: int
    retry_attempts: int
    retry_delay: float

class CacheConfig(BaseModel):
    """Configuration for caching."""
    enabled: bool
    cache_type: str
    redis_url: Optional[str]
    ttl_seconds: int
    max_size: int

class ExecutionConfig(BaseModel):
    """Configuration for task execution."""
    max_concurrent_tasks: int
    task_timeout_seconds: int
    retry_attempts: int
    retry_delay: float
    enable_task_queue: bool
    queue_max_size: int

class PluginConfig(BaseModel):
    """Configuration for plugins."""
    enabled: bool
    plugin_directories: List[str]
    auto_load: bool
    allowed_plugins: List[str]
    blocked_plugins: List[str]
    plugin_timeout_seconds: int

class ContextConfig(BaseModel):
    """Configuration for context management."""
    context_store_type: str
    database_url: Optional[str]
    max_context_size: int
    context_retention_days: int
    enable_semantic_search: bool
    embedding_model: str

class SecurityConfig(BaseModel):
    """Configuration for security settings."""
    enable_authentication: bool
    api_key_required: bool
    allowed_hosts: List[str]
    max_request_size: int
    rate_limit_requests: int
    rate_limit_window_seconds: int

class LoggingConfig(BaseModel):
    """Configuration for logging."""
    level: str
    format: str
    file_path: Optional[str]
    max_file_size: int
    backup_count: int
    enable_console: bool
    enable_file: bool

class MonitoringConfig(BaseModel):
    """Configuration for monitoring."""
    enabled: bool
    metrics_enabled: bool
    performance_monitoring: bool
    health_check_interval: int
    alert_thresholds: Dict[str, float]

class AgentRoleConfig(BaseModel):
    """Configuration for agent roles."""
    name: str
    description: str
    capabilities: List[str]
    model_config: Optional[ModelConfig]
    max_concurrent_tasks: int
    priority: int

class MultiAgentConfig(BaseModel):
    """Configuration for multi-agent coordination."""
    enabled: bool
    max_agents: int
    coordination_strategy: str
    task_delegation_enabled: bool
    result_sharing_enabled: bool
    conflict_resolution_strategy: str
    agent_roles: Dict[str, AgentRoleConfig]
    communication_timeout: int
    coordination_interval: float

class MCPConfig(BaseModel):
    """Configuration for MCP integration."""
    enabled: bool
    servers: List[Dict[str, Any]]
    connection_timeout: int
    request_timeout: int
    max_retries: int
    discovery_enabled: bool

class FrameworkConfig(BaseModel):
    """Main configuration for the agent framework."""
    name: str
    version: str
    debug: bool
    
    model: ModelConfig
    cache: CacheConfig
    execution: ExecutionConfig
    plugins: PluginConfig
    context: ContextConfig
    security: SecurityConfig
    logging: LoggingConfig
    monitoring: MonitoringConfig
    multi_agent: MultiAgentConfig
    mcp: MCPConfig
    
    @classmethod
    def from_file(cls, config_path: str) -> FrameworkConfig: ...
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> FrameworkConfig: ...
    
    def to_dict(self) -> Dict[str, Any]: ...
    
    def save_to_file(self, config_path: str) -> None: ...
    
    def validate_config(self) -> List[str]: ...
