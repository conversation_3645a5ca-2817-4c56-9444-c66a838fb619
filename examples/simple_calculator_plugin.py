"""
Simple Calculator Plugin - Beginner Example

This is a beginner-friendly example showing how to create a basic plugin
with mathematical operations exposed to AI models.
"""

import asyncio
import logging
import math
from typing import Dict, Any

from agent_framework.plugins.enhanced_plugin import EnhancedPlugin
from agent_framework.plugins.decorators import (
    exposed_function, parameter, returns, example,
    float_param, string_param, array_param
)
from agent_framework.plugins.function_registry import ParameterType
from agent_framework.core.types import PluginRequest, PluginResponse


class SimpleCalculatorPlugin(EnhancedPlugin):
    """
    Simple calculator plugin for basic mathematical operations.

    This plugin demonstrates:
    - Basic plugin structure
    - Function exposure with decorators
    - Parameter validation
    - Error handling
    - Documentation and examples
    """

    PLUGIN_NAME = "simple_calculator"
    PLUGIN_VERSION = "1.0.0"
    PLUGIN_DESCRIPTION = "Simple calculator for basic mathematical operations"
    PLUGIN_AUTHOR = "Plugin Developer"
    PLUGIN_LICENSE = "MIT"

    def __init__(self):
        """Initialize the calculator plugin."""
        super().__init__()
        self.calculation_history = []

    @property
    def name(self) -> str:
        """Get the plugin name."""
        return self.PLUGIN_NAME

    async def _initialize_plugin(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin."""
        self.logger.info("Initializing simple calculator plugin")

        # Load configuration
        self.max_history = config.get('max_history', 100)
        self.precision = config.get('precision', 10)

        # Clear history
        self.calculation_history = []

        self.logger.info("Simple calculator plugin initialized")

    async def _cleanup_plugin(self) -> None:
        """Clean up plugin resources."""
        self.logger.info("Cleaning up simple calculator plugin")

        # Clear history
        self.calculation_history.clear()

        self.logger.info("Simple calculator plugin cleaned up")

    async def _execute_capability(self, request: PluginRequest) -> PluginResponse:
        """Execute traditional capability requests."""
        if request.capability == "get_history":
            return PluginResponse(
                success=True,
                result=self.calculation_history.copy(),
                metadata={"plugin_name": self.name,
                          "capability": "get_history"}
            )

        return PluginResponse(
            success=False,
            error=f"Unknown capability: {request.capability}"
        )

    # Basic arithmetic operations

    @exposed_function(
        name="add",
        description="Add two numbers together",
        tags=["math", "arithmetic", "basic"],
        category="arithmetic"
    )
    @float_param("a", "First number", required=True)
    @float_param("b", "Second number", required=True)
    @returns(ParameterType.FLOAT, "Sum of the two numbers")
    @example(
        description="Add two positive numbers",
        input={"a": 5.5, "b": 3.2},
        output=8.7
    )
    @example(
        description="Add positive and negative numbers",
        input={"a": 10, "b": -3},
        output=7
    )
    async def add(self, a: float, b: float) -> float:
        """Add two numbers."""
        result = a + b
        await self._record_calculation("add", [a, b], result)
        return result

    @exposed_function(
        name="subtract",
        description="Subtract the second number from the first",
        tags=["math", "arithmetic", "basic"],
        category="arithmetic"
    )
    @float_param("a", "Number to subtract from", required=True)
    @float_param("b", "Number to subtract", required=True)
    @returns(ParameterType.FLOAT, "Difference of the two numbers")
    @example(
        description="Subtract two numbers",
        input={"a": 10, "b": 3},
        output=7
    )
    async def subtract(self, a: float, b: float) -> float:
        """Subtract two numbers."""
        result = a - b
        await self._record_calculation("subtract", [a, b], result)
        return result

    @exposed_function(
        name="multiply",
        description="Multiply two numbers together",
        tags=["math", "arithmetic", "basic"],
        category="arithmetic"
    )
    @float_param("a", "First number", required=True)
    @float_param("b", "Second number", required=True)
    @returns(ParameterType.FLOAT, "Product of the two numbers")
    @example(
        description="Multiply two numbers",
        input={"a": 4, "b": 2.5},
        output=10.0
    )
    async def multiply(self, a: float, b: float) -> float:
        """Multiply two numbers."""
        result = a * b
        await self._record_calculation("multiply", [a, b], result)
        return result

    @exposed_function(
        name="divide",
        description="Divide the first number by the second",
        tags=["math", "arithmetic", "basic"],
        category="arithmetic"
    )
    @float_param("a", "Dividend (number to be divided)", required=True)
    @float_param("b", "Divisor (number to divide by)", required=True)
    @returns(ParameterType.FLOAT, "Quotient of the division")
    @example(
        description="Divide two numbers",
        input={"a": 15, "b": 3},
        output=5.0
    )
    async def divide(self, a: float, b: float) -> float:
        """Divide two numbers."""
        if b == 0:
            raise ValueError("Cannot divide by zero")

        result = a / b
        await self._record_calculation("divide", [a, b], result)
        return result

    # Advanced operations

    @exposed_function(
        name="power",
        description="Raise the first number to the power of the second",
        tags=["math", "advanced", "exponent"],
        category="advanced"
    )
    @float_param("base", "Base number", required=True)
    @float_param("exponent", "Exponent", required=True)
    @returns(ParameterType.FLOAT, "Result of base raised to exponent")
    @example(
        description="Calculate power",
        input={"base": 2, "exponent": 3},
        output=8.0
    )
    async def power(self, base: float, exponent: float) -> float:
        """Raise base to the power of exponent."""
        try:
            result = base ** exponent
            await self._record_calculation("power", [base, exponent], result)
            return result
        except OverflowError:
            raise ValueError("Result too large to calculate")

    @exposed_function(
        name="square_root",
        description="Calculate the square root of a number",
        tags=["math", "advanced", "root"],
        category="advanced"
    )
    @float_param("number", "Number to find square root of", required=True)
    @returns(ParameterType.FLOAT, "Square root of the number")
    @example(
        description="Calculate square root",
        input={"number": 16},
        output=4.0
    )
    async def square_root(self, number: float) -> float:
        """Calculate square root of a number."""
        if number < 0:
            raise ValueError("Cannot calculate square root of negative number")

        result = math.sqrt(number)
        await self._record_calculation("square_root", [number], result)
        return result

    @exposed_function(
        name="calculate_expression",
        description="Evaluate a mathematical expression safely",
        tags=["math", "expression", "advanced"],
        category="advanced"
    )
    @string_param("expression", "Mathematical expression to evaluate", required=True)
    @returns(ParameterType.FLOAT, "Result of the expression")
    @example(
        description="Evaluate simple expression",
        input={"expression": "2 + 3 * 4"},
        output=14.0
    )
    async def calculate_expression(self, expression: str) -> float:
        """Safely evaluate a mathematical expression."""
        # Simple expression evaluator (for demo purposes)
        # In production, use a proper expression parser

        # Remove whitespace
        expression = expression.replace(" ", "")

        # Basic validation
        allowed_chars = set("0123456789+-*/.()^")
        if not all(c in allowed_chars for c in expression):
            raise ValueError("Expression contains invalid characters")

        # Replace ^ with ** for Python power operator
        expression = expression.replace("^", "**")

        try:
            # Evaluate safely (limited scope)
            result = eval(expression, {"__builtins__": {}}, {})
            await self._record_calculation("expression", [expression], result)
            return float(result)
        except Exception as e:
            raise ValueError(f"Invalid expression: {e}")

    # Utility functions

    @exposed_function(
        name="calculate_average",
        description="Calculate the average of a list of numbers",
        tags=["math", "statistics", "utility"],
        category="statistics"
    )
    @array_param("numbers", "List of numbers to average", required=True)
    @returns(ParameterType.FLOAT, "Average of the numbers")
    @example(
        description="Calculate average of numbers",
        input={"numbers": [1, 2, 3, 4, 5]},
        output=3.0
    )
    async def calculate_average(self, numbers: list) -> float:
        """Calculate the average of a list of numbers."""
        if not numbers:
            raise ValueError("Cannot calculate average of empty list")

        # Validate that all items are numbers
        numeric_values = []
        for item in numbers:
            if isinstance(item, (int, float)):
                numeric_values.append(float(item))
            else:
                raise ValueError(f"Non-numeric value found: {item}")

        result = sum(numeric_values) / len(numeric_values)
        await self._record_calculation("average", numeric_values, result)
        return result

    @exposed_function(
        name="get_calculation_history",
        description="Get the history of calculations performed",
        tags=["history", "utility", "info"],
        category="utility"
    )
    @parameter("limit", ParameterType.INTEGER, "Maximum number of history items to return", required=False, default=10)
    @returns(ParameterType.ARRAY, "List of recent calculations")
    async def get_calculation_history(self, limit: int = 10) -> list:
        """Get recent calculation history."""
        if limit <= 0:
            raise ValueError("Limit must be positive")

        return self.calculation_history[-limit:] if limit < len(self.calculation_history) else self.calculation_history.copy()

    @exposed_function(
        name="clear_history",
        description="Clear the calculation history",
        tags=["history", "utility", "maintenance"],
        category="utility"
    )
    @returns(ParameterType.OBJECT, "Result of clearing history")
    async def clear_history(self) -> dict:
        """Clear calculation history."""
        count = len(self.calculation_history)
        self.calculation_history.clear()

        return {
            "success": True,
            "cleared_count": count,
            "message": f"Cleared {count} calculation(s) from history"
        }

    # Helper methods

    async def _record_calculation(self, operation: str, inputs: list, result: float) -> None:
        """Record a calculation in history."""
        import time

        calculation = {
            "operation": operation,
            "inputs": inputs,
            "result": result,
            "timestamp": time.time()
        }

        self.calculation_history.append(calculation)

        # Keep history within limits
        if len(self.calculation_history) > self.max_history:
            self.calculation_history = self.calculation_history[-self.max_history:]

        self.logger.debug(
            f"Recorded calculation: {operation}({inputs}) = {result}")

    # Hot reload state management

    async def _save_reload_state(self) -> None:
        """Save state before reload."""
        # In a real plugin, you might save to a file
        # For this example, we'll just log
        self.logger.info(
            f"Saving calculator state: {len(self.calculation_history)} calculations")

    async def _restore_reload_state(self) -> None:
        """Restore state after reload."""
        # In a real plugin, you might load from a file
        # For this example, we'll just log
        self.logger.info("Calculator state restored after reload")
