"""
File Operations Plugin - Advanced example demonstrating file system operations.

This plugin provides comprehensive file and directory operations with proper
error handling, security considerations, and state management.
"""

import asyncio
import json
import logging
import os
import shutil
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
import hashlib
import mimetypes

from agent_framework.plugins.enhanced_plugin import EnhancedPlugin
from agent_framework.plugins.decorators import (
    exposed_function, parameter, returns, example, 
    string_param, int_param, bool_param, array_param, object_param,
    validate_input, cached, rate_limited
)
from agent_framework.plugins.function_registry import ParameterType
from agent_framework.core.types import PluginRequest, PluginResponse


class FileOperationsPlugin(EnhancedPlugin):
    """
    Advanced file operations plugin with security and state management.
    
    Provides secure file system operations with sandboxing, validation,
    and comprehensive error handling.
    """
    
    PLUGIN_NAME = "file_operations"
    PLUGIN_VERSION = "1.0.0"
    PLUGIN_DESCRIPTION = "Secure file system operations with sandboxing and validation"
    PLUGIN_AUTHOR = "Agent Framework Team"
    PLUGIN_LICENSE = "MIT"
    
    def __init__(self):
        """Initialize the file operations plugin."""
        super().__init__()
        
        # Security settings
        self._allowed_extensions = {'.txt', '.json', '.csv', '.md', '.py', '.js', '.html', '.css'}
        self._max_file_size = 10 * 1024 * 1024  # 10MB
        self._sandbox_root = Path("sandbox")
        
        # State tracking
        self._operation_history: List[Dict[str, Any]] = []
        self._file_cache: Dict[str, Dict[str, Any]] = {}
    
    @property
    def name(self) -> str:
        """Get the plugin name."""
        return self.PLUGIN_NAME
    
    async def _initialize_plugin(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin."""
        self.logger.info("Initializing file operations plugin")
        
        # Create sandbox directory
        self._sandbox_root.mkdir(exist_ok=True)
        
        # Load configuration
        self._allowed_extensions.update(config.get('allowed_extensions', set()))
        self._max_file_size = config.get('max_file_size', self._max_file_size)
        
        # Load operation history
        await self._load_operation_history()
        
        self.logger.info("File operations plugin initialized")
    
    async def _cleanup_plugin(self) -> None:
        """Clean up plugin resources."""
        self.logger.info("Cleaning up file operations plugin")
        
        # Save operation history
        await self._save_operation_history()
        
        # Clear cache
        self._file_cache.clear()
        
        self.logger.info("File operations plugin cleaned up")
    
    async def _execute_capability(self, request: PluginRequest) -> PluginResponse:
        """Execute traditional capability requests."""
        if request.capability == "list_sandbox":
            try:
                files = list(self._sandbox_root.iterdir())
                return PluginResponse(
                    success=True,
                    result=[str(f) for f in files],
                    metadata={"plugin_name": self.name, "capability": "list_sandbox"}
                )
            except Exception as e:
                return PluginResponse(
                    success=False,
                    error=str(e),
                    metadata={"plugin_name": self.name, "capability": "list_sandbox"}
                )
        
        return PluginResponse(
            success=False,
            error=f"Unknown capability: {request.capability}"
        )
    
    # File reading operations
    
    @exposed_function(
        name="read_file",
        description="Read the contents of a file with encoding detection and validation",
        tags=["file", "read", "io"],
        category="file_operations"
    )
    @string_param("file_path", "Path to the file to read", required=True)
    @string_param("encoding", "File encoding (auto-detect if not specified)", required=False, default="auto")
    @bool_param("validate_size", "Whether to validate file size limits", required=False, default=True)
    @returns(ParameterType.OBJECT, "File contents and metadata")
    @example(
        description="Read a text file",
        input={"file_path": "sandbox/example.txt"},
        output={"content": "Hello World", "size": 11, "encoding": "utf-8", "mime_type": "text/plain"}
    )
    @cached(ttl=60)  # Cache for 1 minute
    async def read_file(self, file_path: str, encoding: str = "auto", validate_size: bool = True) -> Dict[str, Any]:
        """Read file contents with validation and metadata."""
        try:
            # Validate and resolve path
            resolved_path = await self._validate_and_resolve_path(file_path)
            
            # Check if file exists
            if not resolved_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")
            
            if not resolved_path.is_file():
                raise ValueError(f"Path is not a file: {file_path}")
            
            # Validate file size
            file_size = resolved_path.stat().st_size
            if validate_size and file_size > self._max_file_size:
                raise ValueError(f"File too large: {file_size} bytes (max: {self._max_file_size})")
            
            # Detect encoding if needed
            if encoding == "auto":
                encoding = await self._detect_encoding(resolved_path)
            
            # Read file content
            content = resolved_path.read_text(encoding=encoding)
            
            # Get file metadata
            stat = resolved_path.stat()
            mime_type, _ = mimetypes.guess_type(str(resolved_path))
            
            # Record operation
            await self._record_operation("read_file", {
                "file_path": str(resolved_path),
                "size": file_size,
                "encoding": encoding
            })
            
            return {
                "content": content,
                "size": file_size,
                "encoding": encoding,
                "mime_type": mime_type or "application/octet-stream",
                "modified_time": stat.st_mtime,
                "created_time": stat.st_ctime,
                "checksum": hashlib.md5(content.encode(encoding)).hexdigest()
            }
            
        except Exception as e:
            self.logger.error(f"Error reading file {file_path}: {e}")
            raise
    
    @exposed_function(
        name="write_file",
        description="Write content to a file with backup and validation",
        tags=["file", "write", "io"],
        category="file_operations"
    )
    @string_param("file_path", "Path to the file to write", required=True)
    @string_param("content", "Content to write to the file", required=True)
    @string_param("encoding", "File encoding", required=False, default="utf-8")
    @bool_param("create_backup", "Whether to create a backup of existing file", required=False, default=True)
    @bool_param("create_dirs", "Whether to create parent directories", required=False, default=True)
    @returns(ParameterType.OBJECT, "Write operation result")
    @example(
        description="Write content to a file",
        input={"file_path": "sandbox/output.txt", "content": "Hello World"},
        output={"success": True, "bytes_written": 11, "backup_created": False}
    )
    @rate_limited(calls_per_minute=30)  # Rate limit writes
    async def write_file(self, file_path: str, content: str, encoding: str = "utf-8", 
                        create_backup: bool = True, create_dirs: bool = True) -> Dict[str, Any]:
        """Write content to file with backup and validation."""
        try:
            # Validate and resolve path
            resolved_path = await self._validate_and_resolve_path(file_path)
            
            # Validate content size
            content_bytes = content.encode(encoding)
            if len(content_bytes) > self._max_file_size:
                raise ValueError(f"Content too large: {len(content_bytes)} bytes")
            
            # Create parent directories if needed
            if create_dirs:
                resolved_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Create backup if file exists
            backup_created = False
            if resolved_path.exists() and create_backup:
                backup_path = resolved_path.with_suffix(resolved_path.suffix + '.backup')
                shutil.copy2(resolved_path, backup_path)
                backup_created = True
            
            # Write content
            resolved_path.write_text(content, encoding=encoding)
            
            # Record operation
            await self._record_operation("write_file", {
                "file_path": str(resolved_path),
                "size": len(content_bytes),
                "encoding": encoding,
                "backup_created": backup_created
            })
            
            return {
                "success": True,
                "bytes_written": len(content_bytes),
                "backup_created": backup_created,
                "file_path": str(resolved_path),
                "encoding": encoding
            }
            
        except Exception as e:
            self.logger.error(f"Error writing file {file_path}: {e}")
            raise
    
    @exposed_function(
        name="list_directory",
        description="List directory contents with detailed metadata",
        tags=["directory", "list", "filesystem"],
        category="file_operations"
    )
    @string_param("directory_path", "Path to the directory to list", required=True)
    @bool_param("recursive", "Whether to list recursively", required=False, default=False)
    @bool_param("include_hidden", "Whether to include hidden files", required=False, default=False)
    @array_param("file_types", "Filter by file types (extensions)", required=False, default=[])
    @returns(ParameterType.OBJECT, "Directory listing with metadata")
    @example(
        description="List directory contents",
        input={"directory_path": "sandbox", "recursive": False},
        output={"files": [{"name": "example.txt", "type": "file", "size": 11}], "total_files": 1}
    )
    async def list_directory(self, directory_path: str, recursive: bool = False, 
                           include_hidden: bool = False, file_types: List[str] = None) -> Dict[str, Any]:
        """List directory contents with filtering and metadata."""
        try:
            # Validate and resolve path
            resolved_path = await self._validate_and_resolve_path(directory_path)
            
            if not resolved_path.exists():
                raise FileNotFoundError(f"Directory not found: {directory_path}")
            
            if not resolved_path.is_dir():
                raise ValueError(f"Path is not a directory: {directory_path}")
            
            file_types = file_types or []
            files = []
            
            # Get directory contents
            if recursive:
                pattern = "**/*" if include_hidden else "**/[!.]*"
                items = resolved_path.glob(pattern)
            else:
                pattern = "*" if include_hidden else "[!.]*"
                items = resolved_path.glob(pattern)
            
            for item in items:
                # Skip if not matching file types filter
                if file_types and item.suffix.lower() not in [ft.lower() for ft in file_types]:
                    continue
                
                stat = item.stat()
                mime_type, _ = mimetypes.guess_type(str(item))
                
                file_info = {
                    "name": item.name,
                    "path": str(item.relative_to(resolved_path)),
                    "type": "directory" if item.is_dir() else "file",
                    "size": stat.st_size if item.is_file() else 0,
                    "modified_time": stat.st_mtime,
                    "created_time": stat.st_ctime,
                    "mime_type": mime_type,
                    "extension": item.suffix.lower() if item.is_file() else None
                }
                files.append(file_info)
            
            # Sort by name
            files.sort(key=lambda x: (x["type"] == "file", x["name"].lower()))
            
            # Record operation
            await self._record_operation("list_directory", {
                "directory_path": str(resolved_path),
                "recursive": recursive,
                "file_count": len(files)
            })
            
            return {
                "files": files,
                "total_files": len([f for f in files if f["type"] == "file"]),
                "total_directories": len([f for f in files if f["type"] == "directory"]),
                "directory_path": str(resolved_path),
                "recursive": recursive
            }
            
        except Exception as e:
            self.logger.error(f"Error listing directory {directory_path}: {e}")
            raise
    
    @exposed_function(
        name="delete_file",
        description="Delete a file or directory with safety checks",
        tags=["file", "delete", "filesystem"],
        category="file_operations"
    )
    @string_param("file_path", "Path to the file or directory to delete", required=True)
    @bool_param("recursive", "Whether to delete directories recursively", required=False, default=False)
    @bool_param("create_backup", "Whether to create a backup before deletion", required=False, default=True)
    @returns(ParameterType.OBJECT, "Deletion result")
    @example(
        description="Delete a file",
        input={"file_path": "sandbox/temp.txt", "create_backup": True},
        output={"success": True, "backup_created": True, "items_deleted": 1}
    )
    async def delete_file(self, file_path: str, recursive: bool = False, 
                         create_backup: bool = True) -> Dict[str, Any]:
        """Delete file or directory with safety checks."""
        try:
            # Validate and resolve path
            resolved_path = await self._validate_and_resolve_path(file_path)
            
            if not resolved_path.exists():
                raise FileNotFoundError(f"Path not found: {file_path}")
            
            # Safety check for directory deletion
            if resolved_path.is_dir() and not recursive:
                raise ValueError("Cannot delete directory without recursive=True")
            
            backup_created = False
            items_deleted = 0
            
            # Create backup if requested
            if create_backup:
                backup_path = resolved_path.parent / f"{resolved_path.name}.backup.{int(time.time())}"
                if resolved_path.is_file():
                    shutil.copy2(resolved_path, backup_path)
                else:
                    shutil.copytree(resolved_path, backup_path)
                backup_created = True
            
            # Count items to be deleted
            if resolved_path.is_file():
                items_deleted = 1
            else:
                items_deleted = sum(1 for _ in resolved_path.rglob("*"))
            
            # Delete the file or directory
            if resolved_path.is_file():
                resolved_path.unlink()
            else:
                shutil.rmtree(resolved_path)
            
            # Record operation
            await self._record_operation("delete_file", {
                "file_path": str(resolved_path),
                "recursive": recursive,
                "backup_created": backup_created,
                "items_deleted": items_deleted
            })
            
            return {
                "success": True,
                "backup_created": backup_created,
                "items_deleted": items_deleted,
                "file_path": str(resolved_path)
            }
            
        except Exception as e:
            self.logger.error(f"Error deleting {file_path}: {e}")
            raise
    
    @exposed_function(
        name="get_operation_history",
        description="Get the history of file operations performed by this plugin",
        tags=["history", "monitoring", "audit"],
        category="monitoring"
    )
    @int_param("limit", "Maximum number of operations to return", required=False, default=50)
    @string_param("operation_type", "Filter by operation type", required=False)
    @returns(ParameterType.OBJECT, "Operation history")
    async def get_operation_history(self, limit: int = 50, operation_type: str = None) -> Dict[str, Any]:
        """Get file operation history."""
        history = self._operation_history.copy()
        
        # Filter by operation type if specified
        if operation_type:
            history = [op for op in history if op.get("operation") == operation_type]
        
        # Apply limit
        history = history[-limit:] if limit > 0 else history
        
        return {
            "operations": history,
            "total_operations": len(self._operation_history),
            "filtered_count": len(history),
            "operation_types": list(set(op.get("operation") for op in self._operation_history))
        }
    
    # Helper methods
    
    async def _validate_and_resolve_path(self, file_path: str) -> Path:
        """Validate and resolve file path within sandbox."""
        path = Path(file_path)
        
        # Convert to absolute path within sandbox
        if not path.is_absolute():
            resolved_path = (self._sandbox_root / path).resolve()
        else:
            resolved_path = path.resolve()
        
        # Ensure path is within sandbox
        try:
            resolved_path.relative_to(self._sandbox_root.resolve())
        except ValueError:
            raise PermissionError(f"Path outside sandbox: {file_path}")
        
        # Validate file extension for writes
        if resolved_path.suffix and resolved_path.suffix.lower() not in self._allowed_extensions:
            raise ValueError(f"File extension not allowed: {resolved_path.suffix}")
        
        return resolved_path
    
    async def _detect_encoding(self, file_path: Path) -> str:
        """Detect file encoding."""
        try:
            # Try common encodings
            encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        f.read(1024)  # Read first 1KB to test
                    return encoding
                except UnicodeDecodeError:
                    continue
            
            return 'utf-8'  # Default fallback
            
        except Exception:
            return 'utf-8'
    
    async def _record_operation(self, operation: str, details: Dict[str, Any]) -> None:
        """Record file operation for audit trail."""
        operation_record = {
            "operation": operation,
            "timestamp": time.time(),
            "details": details
        }
        
        self._operation_history.append(operation_record)
        
        # Keep only last 1000 operations
        if len(self._operation_history) > 1000:
            self._operation_history = self._operation_history[-1000:]
    
    async def _save_operation_history(self) -> None:
        """Save operation history to file."""
        try:
            history_file = self._sandbox_root / ".operation_history.json"
            with open(history_file, 'w') as f:
                json.dump(self._operation_history, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save operation history: {e}")
    
    async def _load_operation_history(self) -> None:
        """Load operation history from file."""
        try:
            history_file = self._sandbox_root / ".operation_history.json"
            if history_file.exists():
                with open(history_file, 'r') as f:
                    self._operation_history = json.load(f)
        except Exception as e:
            self.logger.error(f"Failed to load operation history: {e}")
            self._operation_history = []
