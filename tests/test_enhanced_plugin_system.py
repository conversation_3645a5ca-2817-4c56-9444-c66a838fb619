"""
Comprehensive unit tests for the enhanced plugin system.

Tests all components including hot reload, function registry, model integration,
error handling, and edge cases.
"""

import asyncio
import json
import pytest
import tempfile
import time
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import sys
import os

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent_framework.core.config import FrameworkConfig, PluginConfig
from agent_framework.plugins import (
    PluginManager, PluginLoader, PluginRegistry, HotReloadManager, HotReloadConfig,
    FunctionRegistry, FunctionMetadata, FunctionCallResult, ParameterType,
    ModelIntegration, ModelFunction, FunctionCallRequest,
    EnhancedPlugin, exposed_function, string_param, int_param, returns
)


class TestPlugin(EnhancedPlugin):
    """Test plugin for unit testing."""
    
    PLUGIN_NAME = "test_plugin"
    PLUGIN_VERSION = "1.0.0"
    PLUGIN_DESCRIPTION = "Test plugin for unit testing"
    
    def __init__(self):
        super().__init__()
        self.initialized = False
        self.cleaned_up = False
        self.test_data = {}
    
    @property
    def name(self) -> str:
        return self.PLUGIN_NAME
    
    async def _initialize_plugin(self, config):
        self.initialized = True
        self.test_data = config.get('test_data', {})
    
    async def _cleanup_plugin(self):
        self.cleaned_up = True
        self.test_data.clear()
    
    async def _execute_capability(self, request):
        from agent_framework.core.types import PluginResponse
        return PluginResponse(
            success=True,
            result=f"Executed {request.capability}",
            metadata={"plugin_name": self.name}
        )
    
    @exposed_function(
        description="Test function that adds two numbers",
        tags=["test", "math"]
    )
    @int_param("a", "First number", required=True)
    @int_param("b", "Second number", required=True)
    @returns(ParameterType.INTEGER, "Sum of the numbers")
    async def add_numbers(self, a: int, b: int) -> int:
        """Add two numbers."""
        return a + b
    
    @exposed_function(
        description="Test function that processes text",
        tags=["test", "text"]
    )
    @string_param("text", "Text to process", required=True)
    @string_param("operation", "Operation to perform", required=False, default="upper")
    @returns(ParameterType.STRING, "Processed text")
    async def process_text(self, text: str, operation: str = "upper") -> str:
        """Process text."""
        if operation == "upper":
            return text.upper()
        elif operation == "lower":
            return text.lower()
        else:
            return text


@pytest.fixture
def temp_plugin_dir():
    """Create a temporary directory for plugin testing."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def config():
    """Create test configuration."""
    config = FrameworkConfig()
    config.plugins.plugin_directories = ["test_plugins"]
    config.plugins.hot_reload_enabled = True
    config.plugins.function_exposure_enabled = True
    return config


@pytest.fixture
def hot_reload_config():
    """Create hot reload configuration."""
    return HotReloadConfig(
        enabled=True,
        debounce_delay=0.1,  # Fast for testing
        max_reload_attempts=3
    )


class TestFunctionRegistry:
    """Test the function registry component."""
    
    @pytest.fixture
    def registry(self):
        """Create a function registry."""
        return FunctionRegistry()
    
    def test_register_function(self, registry):
        """Test function registration."""
        def test_func(x: int, y: str = "default") -> str:
            return f"{x}_{y}"
        
        func_name = registry.register_function(
            function=test_func,
            plugin_name="test",
            description="Test function"
        )
        
        assert func_name == "test.test_func"
        assert func_name in registry.get_all_functions()
        
        metadata = registry.get_function(func_name)
        assert metadata is not None
        assert metadata.plugin_name == "test"
        assert metadata.description == "Test function"
    
    def test_function_parameters_extraction(self, registry):
        """Test automatic parameter extraction."""
        def test_func(x: int, y: str = "default", z: float = 1.0) -> str:
            return f"{x}_{y}_{z}"
        
        func_name = registry.register_function(
            function=test_func,
            plugin_name="test"
        )
        
        metadata = registry.get_function(func_name)
        assert len(metadata.parameters) == 3
        
        # Check parameter details
        param_names = [p.name for p in metadata.parameters]
        assert "x" in param_names
        assert "y" in param_names
        assert "z" in param_names
        
        # Check required parameters
        x_param = next(p for p in metadata.parameters if p.name == "x")
        y_param = next(p for p in metadata.parameters if p.name == "y")
        
        assert x_param.required is True
        assert y_param.required is False
        assert y_param.default == "default"
    
    @pytest.mark.asyncio
    async def test_function_calling(self, registry):
        """Test function calling through registry."""
        def test_func(x: int, y: str = "test") -> str:
            return f"{x}_{y}"
        
        func_name = registry.register_function(
            function=test_func,
            plugin_name="test"
        )
        
        # Test successful call
        result = await registry.call_function(func_name, {"x": 42, "y": "hello"})
        assert result.success is True
        assert result.result == "42_hello"
        
        # Test call with default parameter
        result = await registry.call_function(func_name, {"x": 42})
        assert result.success is True
        assert result.result == "42_test"
        
        # Test validation error
        result = await registry.call_function(func_name, {"y": "hello"})  # Missing required x
        assert result.success is False
        assert "Missing required parameter" in result.error
    
    def test_function_search(self, registry):
        """Test function search functionality."""
        # Register multiple functions
        def func1():
            """Process text data"""
            pass
        
        def func2():
            """Calculate mathematical operations"""
            pass
        
        def func3():
            """Text processing utilities"""
            pass
        
        registry.register_function(func1, "plugin1", "Process text data")
        registry.register_function(func2, "plugin2", "Calculate mathematical operations")
        registry.register_function(func3, "plugin3", "Text processing utilities")
        
        # Search for text-related functions
        results = registry.search_functions("text")
        assert len(results) == 2
        
        # Search for math-related functions
        results = registry.search_functions("math")
        assert len(results) == 1
    
    def test_unregister_functions(self, registry):
        """Test function unregistration."""
        def test_func():
            pass
        
        func_name = registry.register_function(test_func, "test")
        assert func_name in registry.get_all_functions()
        
        # Unregister function
        success = registry.unregister_function(func_name)
        assert success is True
        assert func_name not in registry.get_all_functions()
        
        # Try to unregister non-existent function
        success = registry.unregister_function("non_existent")
        assert success is False
    
    def test_plugin_function_management(self, registry):
        """Test plugin-level function management."""
        def func1():
            pass
        
        def func2():
            pass
        
        registry.register_function(func1, "plugin1")
        registry.register_function(func2, "plugin1")
        
        # Get plugin functions
        plugin_functions = registry.get_plugin_functions("plugin1")
        assert len(plugin_functions) == 2
        
        # Unregister all plugin functions
        count = registry.unregister_plugin_functions("plugin1")
        assert count == 2
        assert len(registry.get_plugin_functions("plugin1")) == 0


class TestEnhancedPlugin:
    """Test the enhanced plugin base class."""
    
    @pytest.mark.asyncio
    async def test_plugin_lifecycle(self):
        """Test plugin initialization and cleanup."""
        plugin = TestPlugin()
        
        # Test initialization
        config = {"test_data": {"key": "value"}}
        await plugin.initialize(config)
        
        assert plugin.initialized is True
        assert plugin.test_data == {"key": "value"}
        assert plugin._is_initialized is True
        
        # Test cleanup
        await plugin.cleanup()
        assert plugin.cleaned_up is True
        assert plugin._is_initialized is False
    
    @pytest.mark.asyncio
    async def test_exposed_function_discovery(self):
        """Test automatic discovery of exposed functions."""
        plugin = TestPlugin()
        await plugin.initialize({})
        
        # Check that exposed functions were discovered
        exposed_functions = plugin.exposed_functions
        assert "add_numbers" in exposed_functions
        assert "process_text" in exposed_functions
        assert len(exposed_functions) == 2
    
    @pytest.mark.asyncio
    async def test_exposed_function_execution(self):
        """Test execution of exposed functions."""
        from agent_framework.core.types import PluginRequest
        
        plugin = TestPlugin()
        await plugin.initialize({})
        
        # Test add_numbers function
        request = PluginRequest(
            capability="add_numbers",
            parameters={"a": 5, "b": 3}
        )
        
        response = await plugin.execute(request)
        assert response.success is True
        assert response.result == 8
        
        # Test process_text function
        request = PluginRequest(
            capability="process_text",
            parameters={"text": "hello", "operation": "upper"}
        )
        
        response = await plugin.execute(request)
        assert response.success is True
        assert response.result == "HELLO"
    
    @pytest.mark.asyncio
    async def test_plugin_capabilities(self):
        """Test plugin capability reporting."""
        plugin = TestPlugin()
        await plugin.initialize({})
        
        capabilities = await plugin.get_capabilities()
        assert len(capabilities) >= 2  # At least the exposed functions
        
        # Check that exposed functions are included as capabilities
        capability_names = [cap.name for cap in capabilities]
        assert "add_numbers" in capability_names
        assert "process_text" in capability_names


class TestModelIntegration:
    """Test the model integration component."""
    
    @pytest.fixture
    async def plugin_manager(self, config, temp_plugin_dir):
        """Create a plugin manager with test plugin."""
        # Create test plugin file
        plugin_file = temp_plugin_dir / "test_plugin.py"
        plugin_code = '''
from agent_framework.plugins import EnhancedPlugin, exposed_function, string_param, int_param, returns
from agent_framework.plugins.function_registry import ParameterType

class TestPlugin(EnhancedPlugin):
    PLUGIN_NAME = "test_plugin"
    
    @property
    def name(self):
        return self.PLUGIN_NAME
    
    async def _initialize_plugin(self, config):
        pass
    
    async def _cleanup_plugin(self):
        pass
    
    async def _execute_capability(self, request):
        from agent_framework.core.types import PluginResponse
        return PluginResponse(success=True, result="test")
    
    @exposed_function(description="Add two numbers")
    @int_param("a", "First number")
    @int_param("b", "Second number")
    @returns(ParameterType.INTEGER, "Sum")
    async def add(self, a: int, b: int) -> int:
        return a + b
'''
        plugin_file.write_text(plugin_code)
        
        # Update config to use temp directory
        config.plugins.plugin_directories = [str(temp_plugin_dir)]
        
        manager = PluginManager(config)
        await manager.initialize()
        
        yield manager
        
        await manager.shutdown()
    
    @pytest.mark.asyncio
    async def test_function_discovery(self, plugin_manager):
        """Test AI model function discovery."""
        integration = ModelIntegration(plugin_manager)
        
        # Get available functions
        functions = integration.get_available_functions()
        assert len(functions) > 0
        
        # Check function format
        for func in functions:
            assert hasattr(func, 'name')
            assert hasattr(func, 'description')
            assert hasattr(func, 'parameters')
    
    @pytest.mark.asyncio
    async def test_function_schemas(self, plugin_manager):
        """Test function schema generation."""
        integration = ModelIntegration(plugin_manager)
        
        # Test OpenAI format
        openai_schemas = integration.get_functions_schema(format="openai")
        assert len(openai_schemas) > 0
        
        for schema in openai_schemas:
            assert "name" in schema
            assert "description" in schema
            assert "parameters" in schema
            assert "type" in schema["parameters"]
            assert "properties" in schema["parameters"]
        
        # Test Anthropic format
        anthropic_schemas = integration.get_functions_schema(format="anthropic")
        assert len(anthropic_schemas) > 0
        
        for schema in anthropic_schemas:
            assert "name" in schema
            assert "description" in schema
            assert "input_schema" in schema
    
    @pytest.mark.asyncio
    async def test_function_calling(self, plugin_manager):
        """Test function calling through model integration."""
        integration = ModelIntegration(plugin_manager)
        
        # Find a test function
        functions = integration.get_available_functions()
        test_function = None
        for func in functions:
            if "add" in func.name.lower():
                test_function = func
                break
        
        if test_function:
            # Test function call
            request = FunctionCallRequest(
                function_name=test_function.name,
                arguments={"a": 5, "b": 3}
            )
            
            result = await integration.call_function(request)
            assert result.success is True
            assert result.result == 8
    
    @pytest.mark.asyncio
    async def test_function_search(self, plugin_manager):
        """Test function search capabilities."""
        integration = ModelIntegration(plugin_manager)
        
        # Search for functions
        results = integration.search_functions("add", max_results=5)
        assert len(results) >= 0  # May or may not find matches
        
        # Check result format
        for result in results:
            assert "name" in result
            assert "description" in result
            assert "relevance_score" in result
    
    @pytest.mark.asyncio
    async def test_call_statistics(self, plugin_manager):
        """Test function call statistics."""
        integration = ModelIntegration(plugin_manager)
        
        # Get initial stats
        initial_stats = integration.get_call_statistics()
        assert "total_calls" in initial_stats
        assert "function_stats" in initial_stats
        
        # Make a function call if possible
        functions = integration.get_available_functions()
        if functions:
            test_function = functions[0]
            request = FunctionCallRequest(
                function_name=test_function.name,
                arguments={}
            )
            
            await integration.call_function(request)
            
            # Check updated stats
            updated_stats = integration.get_call_statistics()
            assert updated_stats["total_calls"] >= initial_stats["total_calls"]


class TestHotReload:
    """Test the hot reload system."""
    
    @pytest.mark.asyncio
    async def test_hot_reload_manager_lifecycle(self, config, hot_reload_config):
        """Test hot reload manager initialization and cleanup."""
        plugin_manager = PluginManager(config, hot_reload_config=hot_reload_config)
        
        # Initialize
        await plugin_manager.initialize()
        assert plugin_manager.is_hot_reload_active() is True
        
        # Cleanup
        await plugin_manager.shutdown()
        assert plugin_manager.is_hot_reload_active() is False
    
    @pytest.mark.asyncio
    async def test_manual_plugin_reload(self, config, hot_reload_config, temp_plugin_dir):
        """Test manual plugin reload."""
        # Create test plugin
        plugin_file = temp_plugin_dir / "reload_test.py"
        plugin_code = '''
from agent_framework.plugins import EnhancedPlugin

class ReloadTestPlugin(EnhancedPlugin):
    PLUGIN_NAME = "reload_test"
    
    @property
    def name(self):
        return self.PLUGIN_NAME
    
    async def _initialize_plugin(self, config):
        pass
    
    async def _cleanup_plugin(self):
        pass
    
    async def _execute_capability(self, request):
        from agent_framework.core.types import PluginResponse
        return PluginResponse(success=True, result="v1")
'''
        plugin_file.write_text(plugin_code)
        
        # Update config
        config.plugins.plugin_directories = [str(temp_plugin_dir)]
        
        plugin_manager = PluginManager(config, hot_reload_config=hot_reload_config)
        await plugin_manager.initialize()
        
        try:
            # Check if plugin was loaded
            status = await plugin_manager.get_plugin_status()
            if status['loaded_count'] > 0:
                # Test manual reload
                reload_event = await plugin_manager.reload_plugin("reload_test", force=True)
                assert reload_event.plugin_name == "reload_test"
                
                # Check reload history
                history = plugin_manager.get_reload_history()
                assert len(history) > 0
        
        finally:
            await plugin_manager.shutdown()


class TestErrorHandling:
    """Test error handling in the plugin system."""
    
    @pytest.mark.asyncio
    async def test_invalid_function_call(self):
        """Test calling non-existent function."""
        registry = FunctionRegistry()
        
        result = await registry.call_function("non_existent_function", {})
        assert result.success is False
        assert "Function not found" in result.error
    
    @pytest.mark.asyncio
    async def test_function_parameter_validation(self):
        """Test function parameter validation."""
        registry = FunctionRegistry()
        
        def test_func(required_param: int) -> int:
            return required_param * 2
        
        func_name = registry.register_function(test_func, "test")
        
        # Test missing required parameter
        result = await registry.call_function(func_name, {})
        assert result.success is False
        assert "Missing required parameter" in result.error
    
    @pytest.mark.asyncio
    async def test_function_execution_error(self):
        """Test handling of function execution errors."""
        registry = FunctionRegistry()
        
        def error_func():
            raise ValueError("Test error")
        
        func_name = registry.register_function(error_func, "test")
        
        result = await registry.call_function(func_name, {})
        assert result.success is False
        assert "Test error" in result.error
    
    @pytest.mark.asyncio
    async def test_plugin_initialization_error(self):
        """Test handling of plugin initialization errors."""
        class ErrorPlugin(EnhancedPlugin):
            @property
            def name(self):
                return "error_plugin"
            
            async def _initialize_plugin(self, config):
                raise RuntimeError("Initialization failed")
            
            async def _cleanup_plugin(self):
                pass
            
            async def _execute_capability(self, request):
                pass
        
        plugin = ErrorPlugin()
        
        with pytest.raises(RuntimeError):
            await plugin.initialize({})


class TestEdgeCases:
    """Test edge cases and boundary conditions."""
    
    def test_empty_function_registry(self):
        """Test operations on empty function registry."""
        registry = FunctionRegistry()
        
        assert len(registry.get_all_functions()) == 0
        assert registry.get_function("non_existent") is None
        assert len(registry.search_functions("anything")) == 0
        
        stats = registry.get_registry_stats()
        assert stats["total_functions"] == 0
    
    @pytest.mark.asyncio
    async def test_plugin_double_initialization(self):
        """Test double initialization of plugin."""
        plugin = TestPlugin()
        
        # First initialization
        await plugin.initialize({})
        assert plugin.initialized is True
        
        # Second initialization should not cause issues
        await plugin.initialize({})
        assert plugin.initialized is True  # Still initialized
    
    def test_function_registration_edge_cases(self):
        """Test edge cases in function registration."""
        registry = FunctionRegistry()
        
        # Function with no parameters
        def no_params_func():
            return "test"
        
        func_name = registry.register_function(no_params_func, "test")
        metadata = registry.get_function(func_name)
        assert len(metadata.parameters) == 0
        
        # Function with complex type hints
        def complex_func(data: dict, items: list) -> str:
            return "test"
        
        func_name = registry.register_function(complex_func, "test")
        metadata = registry.get_function(func_name)
        assert len(metadata.parameters) == 2
    
    @pytest.mark.asyncio
    async def test_concurrent_function_calls(self):
        """Test concurrent function calls."""
        registry = FunctionRegistry()
        
        async def slow_func(delay: float = 0.1) -> str:
            await asyncio.sleep(delay)
            return "done"
        
        func_name = registry.register_function(slow_func, "test")
        
        # Make multiple concurrent calls
        tasks = [
            registry.call_function(func_name, {"delay": 0.05})
            for _ in range(5)
        ]
        
        results = await asyncio.gather(*tasks)
        
        # All calls should succeed
        for result in results:
            assert result.success is True
            assert result.result == "done"


@pytest.mark.integration
class TestPluginSystemIntegration:
    """Integration tests for the complete plugin system."""

    @pytest.mark.asyncio
    async def test_full_plugin_lifecycle_integration(self, config, temp_plugin_dir):
        """Test complete plugin lifecycle with all components."""
        # Create a comprehensive test plugin
        plugin_file = temp_plugin_dir / "integration_test.py"
        plugin_code = '''
from agent_framework.plugins import EnhancedPlugin, exposed_function, string_param, int_param, returns
from agent_framework.plugins.function_registry import ParameterType

class IntegrationTestPlugin(EnhancedPlugin):
    PLUGIN_NAME = "integration_test"
    PLUGIN_VERSION = "1.0.0"
    PLUGIN_DESCRIPTION = "Integration test plugin"

    def __init__(self):
        super().__init__()
        self.call_count = 0

    @property
    def name(self):
        return self.PLUGIN_NAME

    async def _initialize_plugin(self, config):
        self.call_count = 0

    async def _cleanup_plugin(self):
        pass

    async def _execute_capability(self, request):
        from agent_framework.core.types import PluginResponse
        return PluginResponse(success=True, result="capability_executed")

    @exposed_function(
        description="Multiply two numbers",
        tags=["math", "calculation"]
    )
    @int_param("a", "First number", required=True)
    @int_param("b", "Second number", required=True)
    @returns(ParameterType.INTEGER, "Product of the numbers")
    async def multiply(self, a: int, b: int) -> int:
        self.call_count += 1
        return a * b

    @exposed_function(
        description="Format a greeting message",
        tags=["text", "formatting"]
    )
    @string_param("name", "Name to greet", required=True)
    @string_param("greeting", "Greeting type", required=False, default="Hello")
    @returns(ParameterType.STRING, "Formatted greeting")
    async def greet(self, name: str, greeting: str = "Hello") -> str:
        self.call_count += 1
        return f"{greeting}, {name}!"
'''
        plugin_file.write_text(plugin_code)

        # Update config
        config.plugins.plugin_directories = [str(temp_plugin_dir)]

        # Initialize plugin manager
        plugin_manager = PluginManager(config)
        await plugin_manager.initialize()

        try:
            # Test plugin discovery and loading
            status = await plugin_manager.get_plugin_status()
            assert status['loaded_count'] > 0

            # Test function discovery
            functions = plugin_manager.get_available_functions()
            assert len(functions) >= 2

            function_names = list(functions.keys())
            assert any("multiply" in name for name in function_names)
            assert any("greet" in name for name in function_names)

            # Test model integration
            model_integration = ModelIntegration(plugin_manager)

            # Test function calling through model integration
            multiply_func = next((name for name in function_names if "multiply" in name), None)
            if multiply_func:
                request = FunctionCallRequest(
                    function_name=multiply_func,
                    arguments={"a": 6, "b": 7}
                )
                result = await model_integration.call_function(request)
                assert result.success is True
                assert result.result == 42

            greet_func = next((name for name in function_names if "greet" in name), None)
            if greet_func:
                request = FunctionCallRequest(
                    function_name=greet_func,
                    arguments={"name": "World"}
                )
                result = await model_integration.call_function(request)
                assert result.success is True
                assert result.result == "Hello, World!"

            # Test function search
            search_results = model_integration.search_functions("math")
            assert len(search_results) > 0

            # Test statistics
            stats = model_integration.get_call_statistics()
            assert stats["total_calls"] >= 2

            # Test schema generation
            schemas = model_integration.get_functions_schema(format="openai")
            assert len(schemas) >= 2

        finally:
            await plugin_manager.shutdown()

    @pytest.mark.asyncio
    async def test_plugin_dependency_resolution(self, config, temp_plugin_dir):
        """Test plugin dependency resolution and loading order."""
        # Create dependent plugins
        base_plugin_file = temp_plugin_dir / "base_plugin.py"
        base_plugin_code = '''
from agent_framework.plugins import EnhancedPlugin, exposed_function, string_param, returns
from agent_framework.plugins.function_registry import ParameterType

class BasePlugin(EnhancedPlugin):
    PLUGIN_NAME = "base_plugin"

    @property
    def name(self):
        return self.PLUGIN_NAME

    async def _initialize_plugin(self, config):
        pass

    async def _cleanup_plugin(self):
        pass

    async def _execute_capability(self, request):
        from agent_framework.core.types import PluginResponse
        return PluginResponse(success=True, result="base")

    @exposed_function(description="Base function")
    @string_param("text", "Text to process")
    @returns(ParameterType.STRING, "Processed text")
    async def base_function(self, text: str) -> str:
        return f"base:{text}"
'''
        base_plugin_file.write_text(base_plugin_code)

        dependent_plugin_file = temp_plugin_dir / "dependent_plugin.py"
        dependent_plugin_code = '''
from agent_framework.plugins import EnhancedPlugin, exposed_function, string_param, returns
from agent_framework.plugins.function_registry import ParameterType

class DependentPlugin(EnhancedPlugin):
    PLUGIN_NAME = "dependent_plugin"
    PLUGIN_DEPENDENCIES = ["base_plugin"]

    @property
    def name(self):
        return self.PLUGIN_NAME

    async def _initialize_plugin(self, config):
        pass

    async def _cleanup_plugin(self):
        pass

    async def _execute_capability(self, request):
        from agent_framework.core.types import PluginResponse
        return PluginResponse(success=True, result="dependent")

    @exposed_function(description="Dependent function")
    @string_param("text", "Text to process")
    @returns(ParameterType.STRING, "Processed text")
    async def dependent_function(self, text: str) -> str:
        return f"dependent:{text}"
'''
        dependent_plugin_file.write_text(dependent_plugin_code)

        # Update config
        config.plugins.plugin_directories = [str(temp_plugin_dir)]

        plugin_manager = PluginManager(config)
        await plugin_manager.initialize()

        try:
            # Check that both plugins are loaded
            status = await plugin_manager.get_plugin_status()
            assert status['loaded_count'] >= 2

            # Check that functions from both plugins are available
            functions = plugin_manager.get_available_functions()
            function_names = list(functions.keys())

            assert any("base_function" in name for name in function_names)
            assert any("dependent_function" in name for name in function_names)

        finally:
            await plugin_manager.shutdown()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
